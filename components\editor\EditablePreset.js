import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Box,
  Divider,
  <PERSON>,
  Alert,
  CircularProgress
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import { Save, Cancel, Edit, Undo, Redo } from '@material-ui/icons'
import ParameterControl from './ParameterControl'
import { childParser } from '../../lib'
import { editor } from '../../lib/editor'

const useStyles = makeStyles((theme) => ({
  presetCard: {
    margin: theme.spacing(2),
    minWidth: 400,
  },
  presetHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  presetName: {
    color: theme.palette.primary.main,
    fontWeight: 'bold',
  },
  editModeChip: {
    backgroundColor: theme.palette.warning.light,
    color: theme.palette.warning.contrastText,
  },
  parametersContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(1),
  },
  readOnlyParameter: {
    display: 'flex',
    justifyContent: 'space-between',
    padding: theme.spacing(0.5),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  parameterName: {
    fontWeight: 'bold',
  },
  parameterValue: {
    color: theme.palette.text.secondary,
  },
  actionButtons: {
    display: 'flex',
    gap: theme.spacing(1),
    justifyContent: 'flex-end',
  },
  validationErrors: {
    marginTop: theme.spacing(1),
  },
  undoRedoButtons: {
    marginRight: 'auto',
  }
}))

/**
 * Editable preset component that extends the original Preset component
 * with editing capabilities
 */
const EditablePreset = ({ 
  name, 
  preset, 
  presetPath,
  blockType,
  onSave,
  onCancel,
  readOnly = false 
}) => {
  const classes = useStyles()
  const [editMode, setEditMode] = useState(false)
  const [presetData, setPresetData] = useState(preset)
  const [originalData, setOriginalData] = useState(preset)
  const [isDirty, setIsDirty] = useState(false)
  const [validationErrors, setValidationErrors] = useState([])
  const [isValid, setIsValid] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [undoStack, setUndoStack] = useState([])
  const [redoStack, setRedoStack] = useState([])

  // Parse preset data
  const { content } = presetData
  const { data } = JSON.parse(content)
  const dataValues = Object.values(data)
  const { childorder, children } = dataValues[0]

  // Update preset data when prop changes
  useEffect(() => {
    setPresetData(preset)
    setOriginalData(preset)
    setIsDirty(false)
    setValidationErrors([])
    setIsValid(true)
  }, [preset])

  // Handle entering edit mode
  const handleEnterEditMode = () => {
    setEditMode(true)
    setOriginalData(JSON.parse(JSON.stringify(presetData)))
    setUndoStack([])
    setRedoStack([])
  }

  // Handle exiting edit mode
  const handleExitEditMode = () => {
    if (isDirty) {
      const confirmExit = window.confirm(
        'You have unsaved changes. Are you sure you want to exit edit mode?'
      )
      if (!confirmExit) return
    }
    
    setEditMode(false)
    setPresetData(originalData)
    setIsDirty(false)
    setValidationErrors([])
    setIsValid(true)
    setUndoStack([])
    setRedoStack([])
  }

  // Handle parameter change
  const handleParameterChange = (paramName, newValue) => {
    // Add current state to undo stack
    setUndoStack(prev => [...prev, JSON.parse(JSON.stringify(presetData))])
    setRedoStack([]) // Clear redo stack on new change

    // Update the preset data
    try {
      const updatedData = editor.core.updatePresetParameter(presetData, paramName, newValue)
      setPresetData(updatedData)
      setIsDirty(true)
      
      // Validate the updated preset
      const validation = editor.validator.validatePreset(updatedData)
      setValidationErrors(validation.errors || [])
      setIsValid(validation.success)
      
    } catch (error) {
      console.error('Error updating parameter:', error)
      setValidationErrors([{ message: error.message }])
      setIsValid(false)
    }
  }

  // Handle parameter validation
  const handleParameterValidation = (paramName, validation) => {
    // Update validation state for individual parameters
    // This could be expanded to track per-parameter validation
  }

  // Handle undo
  const handleUndo = () => {
    if (undoStack.length > 0) {
      const previousState = undoStack[undoStack.length - 1]
      setRedoStack(prev => [presetData, ...prev])
      setUndoStack(prev => prev.slice(0, -1))
      setPresetData(previousState)
      
      // Check if we're back to original state
      const isBackToOriginal = JSON.stringify(previousState) === JSON.stringify(originalData)
      setIsDirty(!isBackToOriginal)
    }
  }

  // Handle redo
  const handleRedo = () => {
    if (redoStack.length > 0) {
      const nextState = redoStack[0]
      setUndoStack(prev => [...prev, presetData])
      setRedoStack(prev => prev.slice(1))
      setPresetData(nextState)
      setIsDirty(true)
    }
  }

  // Handle save
  const handleSave = async () => {
    if (!isValid) {
      alert('Cannot save preset with validation errors')
      return
    }

    setIsSaving(true)
    try {
      const result = await editor.savePreset(presetPath, presetData)
      
      if (result.success) {
        setOriginalData(JSON.parse(JSON.stringify(presetData)))
        setIsDirty(false)
        setEditMode(false)
        
        if (onSave) {
          onSave(presetData, result)
        }
      } else {
        alert(`Failed to save preset: ${result.message}`)
      }
    } catch (error) {
      console.error('Save error:', error)
      alert(`Error saving preset: ${error.message}`)
    } finally {
      setIsSaving(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      handleExitEditMode()
    }
  }

  // Render read-only parameter
  const renderReadOnlyParameter = (paramName) => {
    const paramValue = childParser(children[paramName])
    return (
      <Box key={paramName} className={classes.readOnlyParameter}>
        <Typography className={classes.parameterName}>
          {paramName}:
        </Typography>
        <Typography className={classes.parameterValue}>
          {paramValue}
        </Typography>
      </Box>
    )
  }

  // Render editable parameter
  const renderEditableParameter = (paramName) => {
    const paramData = children[paramName]
    const currentValue = paramData.value || paramData.string || paramData.state
    
    return (
      <ParameterControl
        key={paramName}
        paramName={paramName}
        value={currentValue}
        blockType={blockType}
        onChange={handleParameterChange}
        onValidation={handleParameterValidation}
        disabled={isSaving}
      />
    )
  }

  return (
    <Card className={classes.presetCard}>
      <CardContent>
        <Box className={classes.presetHeader}>
          <Typography variant="h6" className={classes.presetName}>
            {name}
          </Typography>
          {editMode && (
            <Chip 
              label="EDITING" 
              size="small" 
              className={classes.editModeChip}
            />
          )}
        </Box>

        <Divider />

        <Box className={classes.parametersContainer}>
          {childorder.map(paramName => 
            editMode 
              ? renderEditableParameter(paramName)
              : renderReadOnlyParameter(paramName)
          )}
        </Box>

        {validationErrors.length > 0 && (
          <Box className={classes.validationErrors}>
            {validationErrors.map((error, index) => (
              <Alert key={index} severity="error" size="small">
                {error.message}
              </Alert>
            ))}
          </Box>
        )}
      </CardContent>

      <CardActions className={classes.actionButtons}>
        {editMode ? (
          <>
            <Box className={classes.undoRedoButtons}>
              <Button
                size="small"
                onClick={handleUndo}
                disabled={undoStack.length === 0 || isSaving}
                startIcon={<Undo />}
              >
                Undo
              </Button>
              <Button
                size="small"
                onClick={handleRedo}
                disabled={redoStack.length === 0 || isSaving}
                startIcon={<Redo />}
              >
                Redo
              </Button>
            </Box>
            
            <Button
              size="small"
              onClick={handleCancel}
              disabled={isSaving}
              startIcon={<Cancel />}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              color="primary"
              onClick={handleSave}
              disabled={!isDirty || !isValid || isSaving}
              startIcon={isSaving ? <CircularProgress size={16} /> : <Save />}
            >
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
          </>
        ) : (
          !readOnly && (
            <Button
              size="small"
              variant="outlined"
              onClick={handleEnterEditMode}
              startIcon={<Edit />}
            >
              Edit
            </Button>
          )
        )}
      </CardActions>
    </Card>
  )
}

export default EditablePreset
