import React, { useState, useRef } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Button,
  Menu,
  MenuItem,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import {
  DragIndicator,
  Add,
  Delete,
  ArrowForward,
  Settings
} from '@material-ui/icons'
import { blockColor } from '../../lib'

const useStyles = makeStyles((theme) => ({
  signalChain: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
    padding: theme.spacing(2),
    overflowX: 'auto',
    minHeight: 120,
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
    border: `2px dashed ${theme.palette.grey[300]}`,
  },
  signalChainEditing: {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.light + '10',
  },
  effectBlock: {
    minWidth: 150,
    maxWidth: 200,
    cursor: 'grab',
    transition: 'all 0.2s ease',
    position: 'relative',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[4],
    }
  },
  effectBlockDragging: {
    cursor: 'grabbing',
    transform: 'rotate(5deg)',
    opacity: 0.8,
    zIndex: 1000,
  },
  effectBlockDropTarget: {
    borderColor: theme.palette.primary.main,
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  effectHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(1),
  },
  effectName: {
    fontWeight: 'bold',
    fontSize: '0.875rem',
  },
  effectControls: {
    display: 'flex',
    gap: theme.spacing(0.5),
  },
  dragHandle: {
    cursor: 'grab',
    color: theme.palette.text.secondary,
    '&:hover': {
      color: theme.palette.text.primary,
    }
  },
  presetName: {
    fontSize: '0.75rem',
    color: theme.palette.text.secondary,
    fontStyle: 'italic',
  },
  signalArrow: {
    color: theme.palette.text.secondary,
    fontSize: '1.5rem',
  },
  addEffectButton: {
    minWidth: 100,
    height: 80,
    border: `2px dashed ${theme.palette.grey[400]}`,
    backgroundColor: 'transparent',
    '&:hover': {
      borderColor: theme.palette.primary.main,
      backgroundColor: theme.palette.primary.light + '20',
    }
  },
  routingInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
  routingChip: {
    fontSize: '0.75rem',
  },
  dropZone: {
    minWidth: 20,
    height: 80,
    border: `2px dashed transparent`,
    borderRadius: theme.shape.borderRadius,
    transition: 'all 0.2s ease',
  },
  dropZoneActive: {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.light + '30',
  }
}))

/**
 * Signal chain editor component with drag-and-drop functionality
 */
const SignalChainEditor = ({
  rigData,
  onReorder,
  onAddEffect,
  onRemoveEffect,
  onEditEffect,
  editMode = false,
  disabled = false
}) => {
  const classes = useStyles()
  const [draggedItem, setDraggedItem] = useState(null)
  const [dropTarget, setDropTarget] = useState(null)
  const [addMenuAnchor, setAddMenuAnchor] = useState(null)
  const [addPosition, setAddPosition] = useState(0)
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [effectToDelete, setEffectToDelete] = useState(null)
  
  const dragCounter = useRef(0)

  // Parse rig data
  const { content } = rigData
  const contentJson = JSON.parse(content)
  const { data } = contentJson
  const { Patch: { childorder: blockOrder, children: blockChildren } } = data

  // Get routing information
  const routing = blockChildren?.Chain?.children?.Routing?.string ?? 'S'
  
  // Filter out system blocks and get effect blocks
  const effectBlocks = blockOrder.filter(block => 
    !['Chain', 'Rig', 'Input'].includes(block)
  )

  // Available effects to add
  const availableEffects = [
    'Amp', 'Cab', 'Delay', 'Reverb', 'Chorus', 'Flanger', 
    'Phaser', 'Compressor', 'Distortion', 'Overdrive', 
    'EQ', 'Wah', 'Tremolo', 'Octave'
  ]

  // Handle drag start
  const handleDragStart = (e, blockName, index) => {
    if (!editMode || disabled) return
    
    setDraggedItem({ blockName, index })
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', blockName)
    
    // Add dragging class after a short delay to avoid flickering
    setTimeout(() => {
      e.target.classList.add(classes.effectBlockDragging)
    }, 0)
  }

  // Handle drag end
  const handleDragEnd = (e) => {
    e.target.classList.remove(classes.effectBlockDragging)
    setDraggedItem(null)
    setDropTarget(null)
    dragCounter.current = 0
  }

  // Handle drag over
  const handleDragOver = (e) => {
    if (!editMode || disabled) return
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  // Handle drag enter
  const handleDragEnter = (e, targetIndex) => {
    if (!editMode || disabled) return
    e.preventDefault()
    dragCounter.current++
    setDropTarget(targetIndex)
  }

  // Handle drag leave
  const handleDragLeave = (e) => {
    if (!editMode || disabled) return
    dragCounter.current--
    if (dragCounter.current === 0) {
      setDropTarget(null)
    }
  }

  // Handle drop
  const handleDrop = (e, targetIndex) => {
    if (!editMode || disabled) return
    e.preventDefault()
    
    if (draggedItem && draggedItem.index !== targetIndex) {
      const newOrder = [...effectBlocks]
      const [movedItem] = newOrder.splice(draggedItem.index, 1)
      newOrder.splice(targetIndex, 0, movedItem)
      
      if (onReorder) {
        // Include system blocks in the correct positions
        const fullOrder = ['Input', ...newOrder, 'Output']
        onReorder(fullOrder)
      }
    }
    
    setDraggedItem(null)
    setDropTarget(null)
    dragCounter.current = 0
  }

  // Handle add effect menu
  const handleAddEffectClick = (e, position) => {
    setAddMenuAnchor(e.currentTarget)
    setAddPosition(position)
  }

  const handleAddEffectClose = () => {
    setAddMenuAnchor(null)
  }

  const handleAddEffect = (effectType) => {
    if (onAddEffect) {
      onAddEffect(effectType, addPosition)
    }
    handleAddEffectClose()
  }

  // Handle remove effect
  const handleRemoveEffect = (blockName) => {
    setEffectToDelete(blockName)
    setDeleteConfirmOpen(true)
  }

  const confirmRemoveEffect = () => {
    if (onRemoveEffect && effectToDelete) {
      onRemoveEffect(effectToDelete)
    }
    setDeleteConfirmOpen(false)
    setEffectToDelete(null)
  }

  // Render effect block
  const renderEffectBlock = (blockName, index) => {
    const blockData = blockChildren[blockName]
    if (!blockData) return null

    const presetName = blockData.children?.PresetName?.string || 'Default'
    const colorSetting = blockData.children?.Colour
    const backgroundColor = blockColor(colorSetting)
    
    const isDragging = draggedItem?.blockName === blockName
    const isDropTarget = dropTarget === index

    return (
      <Card
        key={blockName}
        className={`${classes.effectBlock} ${isDragging ? classes.effectBlockDragging : ''} ${isDropTarget ? classes.effectBlockDropTarget : ''}`}
        style={{ backgroundColor }}
        draggable={editMode && !disabled}
        onDragStart={(e) => handleDragStart(e, blockName, index)}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
        onDragEnter={(e) => handleDragEnter(e, index)}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, index)}
      >
        <CardContent>
          <Box className={classes.effectHeader}>
            <Typography className={classes.effectName}>
              {blockName}
            </Typography>
            {editMode && !disabled && (
              <Box className={classes.effectControls}>
                <Tooltip title="Drag to reorder">
                  <IconButton
                    size="small"
                    className={classes.dragHandle}
                  >
                    <DragIndicator />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Edit effect">
                  <IconButton
                    size="small"
                    onClick={() => onEditEffect && onEditEffect(blockName)}
                  >
                    <Settings />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Remove effect">
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveEffect(blockName)}
                    disabled={blockName === 'Output'} // Prevent removing output
                  >
                    <Delete />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </Box>
          <Typography className={classes.presetName}>
            {presetName}
          </Typography>
        </CardContent>
      </Card>
    )
  }

  // Render drop zone
  const renderDropZone = (index) => {
    if (!editMode || disabled) return null
    
    const isActive = dropTarget === index
    
    return (
      <Box
        key={`dropzone-${index}`}
        className={`${classes.dropZone} ${isActive ? classes.dropZoneActive : ''}`}
        onDragOver={handleDragOver}
        onDragEnter={(e) => handleDragEnter(e, index)}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, index)}
      />
    )
  }

  // Render add effect button
  const renderAddEffectButton = (position) => {
    if (!editMode || disabled) return null
    
    return (
      <Button
        key={`add-${position}`}
        className={classes.addEffectButton}
        onClick={(e) => handleAddEffectClick(e, position)}
        startIcon={<Add />}
      >
        Add Effect
      </Button>
    )
  }

  return (
    <Box>
      {/* Routing Information */}
      <Box className={classes.routingInfo}>
        <Typography variant="subtitle2">Signal Routing:</Typography>
        <Chip
          size="small"
          label={routing === 'S' ? 'Serial' : 'Parallel-Serial'}
          className={classes.routingChip}
        />
      </Box>

      {/* Signal Chain */}
      <Box className={`${classes.signalChain} ${editMode ? classes.signalChainEditing : ''}`}>
        {/* Input */}
        <Card className={classes.effectBlock}>
          <CardContent>
            <Typography className={classes.effectName}>Input</Typography>
          </CardContent>
        </Card>

        <ArrowForward className={classes.signalArrow} />

        {/* Add effect at beginning */}
        {renderAddEffectButton(0)}

        {/* Effect blocks with drop zones */}
        {effectBlocks.map((blockName, index) => (
          <React.Fragment key={blockName}>
            {renderDropZone(index)}
            {renderEffectBlock(blockName, index)}
            <ArrowForward className={classes.signalArrow} />
            {renderAddEffectButton(index + 1)}
          </React.Fragment>
        ))}

        {/* Final drop zone */}
        {renderDropZone(effectBlocks.length)}
      </Box>

      {/* Add Effect Menu */}
      <Menu
        anchorEl={addMenuAnchor}
        open={Boolean(addMenuAnchor)}
        onClose={handleAddEffectClose}
      >
        {availableEffects.map((effectType) => (
          <MenuItem
            key={effectType}
            onClick={() => handleAddEffect(effectType)}
          >
            {effectType}
          </MenuItem>
        ))}
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Remove Effect</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove the {effectToDelete} effect from the signal chain?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>
            Cancel
          </Button>
          <Button onClick={confirmRemoveEffect} color="secondary">
            Remove
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default SignalChainEditor
