import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Ty<PERSON>graphy,
  Button,
  Divider,
  <PERSON>,
  Alert,
  CircularProgress,
  Tabs,
  Tab
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import { Save, Cancel, Edit, Undo, Redo } from '@material-ui/icons'
import SignalChainEditor from './SignalChainEditor'
import EditableRigBlock from './EditableRigBlock'
import { editor } from '../../lib/editor'

const useStyles = makeStyles((theme) => ({
  rigCard: {
    margin: theme.spacing(2),
    minWidth: 800,
  },
  rigHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  rigName: {
    color: theme.palette.primary.main,
    fontWeight: 'bold',
  },
  editModeChip: {
    backgroundColor: theme.palette.warning.light,
    color: theme.palette.warning.contrastText,
  },
  tabPanel: {
    paddingTop: theme.spacing(2),
  },
  signalChainSection: {
    marginBottom: theme.spacing(3),
  },
  blocksSection: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: theme.spacing(2),
  },
  actionButtons: {
    display: 'flex',
    gap: theme.spacing(1),
    justifyContent: 'flex-end',
  },
  validationErrors: {
    marginTop: theme.spacing(1),
  },
  undoRedoButtons: {
    marginRight: 'auto',
  }
}))

/**
 * Editable rig component that extends the original Rig component
 * with comprehensive editing capabilities
 */
const EditableRig = ({
  name,
  rigData,
  rigPath,
  onSave,
  onCancel,
  readOnly = false
}) => {
  const classes = useStyles()
  const [editMode, setEditMode] = useState(false)
  const [currentRigData, setCurrentRigData] = useState(rigData)
  const [originalData, setOriginalData] = useState(rigData)
  const [isDirty, setIsDirty] = useState(false)
  const [validationErrors, setValidationErrors] = useState([])
  const [isValid, setIsValid] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [undoStack, setUndoStack] = useState([])
  const [redoStack, setRedoStack] = useState([])
  const [activeTab, setActiveTab] = useState(0)
  const [editingBlock, setEditingBlock] = useState(null)

  // Parse rig data
  const shortName = name.replace('.rig', '')
  const { content } = currentRigData
  const contentJson = JSON.parse(content)
  const { data } = contentJson
  const { Patch: { childorder: blockOrder, children: blockChildren } } = data

  // Update rig data when prop changes
  useEffect(() => {
    setCurrentRigData(rigData)
    setOriginalData(rigData)
    setIsDirty(false)
    setValidationErrors([])
    setIsValid(true)
  }, [rigData])

  // Handle entering edit mode
  const handleEnterEditMode = () => {
    setEditMode(true)
    setOriginalData(JSON.parse(JSON.stringify(currentRigData)))
    setUndoStack([])
    setRedoStack([])
  }

  // Handle exiting edit mode
  const handleExitEditMode = () => {
    if (isDirty) {
      const confirmExit = window.confirm(
        'You have unsaved changes. Are you sure you want to exit edit mode?'
      )
      if (!confirmExit) return
    }
    
    setEditMode(false)
    setCurrentRigData(originalData)
    setIsDirty(false)
    setValidationErrors([])
    setIsValid(true)
    setUndoStack([])
    setRedoStack([])
    setEditingBlock(null)
  }

  // Add current state to undo stack
  const addToUndoStack = (data) => {
    setUndoStack(prev => [...prev, JSON.parse(JSON.stringify(data))])
    setRedoStack([]) // Clear redo stack on new change
  }

  // Handle signal chain reorder
  const handleSignalChainReorder = async (newOrder) => {
    addToUndoStack(currentRigData)
    
    try {
      const result = await editor.updateRigSignalChain(rigPath, currentRigData, newOrder)
      
      if (result.success) {
        setCurrentRigData(result.data)
        setIsDirty(true)
        setValidationErrors([])
        setIsValid(true)
      } else {
        setValidationErrors(result.errors || [{ message: result.error }])
        setIsValid(false)
      }
    } catch (error) {
      console.error('Error reordering signal chain:', error)
      setValidationErrors([{ message: error.message }])
      setIsValid(false)
    }
  }

  // Handle add effect to signal chain
  const handleAddEffect = async (effectType, position) => {
    addToUndoStack(currentRigData)
    
    try {
      const result = await editor.addEffectToRig(rigPath, currentRigData, effectType, position)
      
      if (result.success) {
        setCurrentRigData(result.data)
        setIsDirty(true)
        setValidationErrors([])
        setIsValid(true)
      } else {
        setValidationErrors(result.errors || [{ message: result.error }])
        setIsValid(false)
      }
    } catch (error) {
      console.error('Error adding effect:', error)
      setValidationErrors([{ message: error.message }])
      setIsValid(false)
    }
  }

  // Handle remove effect from signal chain
  const handleRemoveEffect = async (effectId) => {
    addToUndoStack(currentRigData)
    
    try {
      const result = await editor.removeEffectFromRig(rigPath, currentRigData, effectId)
      
      if (result.success) {
        setCurrentRigData(result.data)
        setIsDirty(true)
        setValidationErrors([])
        setIsValid(true)
      } else {
        setValidationErrors(result.errors || [{ message: result.error }])
        setIsValid(false)
      }
    } catch (error) {
      console.error('Error removing effect:', error)
      setValidationErrors([{ message: error.message }])
      setIsValid(false)
    }
  }

  // Handle edit effect
  const handleEditEffect = (blockName) => {
    setEditingBlock(blockName)
    setActiveTab(1) // Switch to blocks tab
  }

  // Handle block parameter change
  const handleBlockParameterChange = (blockName, paramName, newValue) => {
    addToUndoStack(currentRigData)
    
    try {
      // Update the block parameter in the rig data
      const updatedData = JSON.parse(JSON.stringify(currentRigData))
      const content = JSON.parse(updatedData.content)
      const blockData = content.data.Patch.children[blockName]
      
      if (blockData && blockData.children[paramName]) {
        const paramType = typeof newValue === 'boolean' ? 1 : 0
        blockData.children[paramName] = {
          type: paramType,
          ...(paramType === 1 ? { state: newValue } : 
              typeof newValue === 'string' ? { string: newValue } : { value: newValue })
        }
      }
      
      updatedData.content = JSON.stringify(content)
      setCurrentRigData(updatedData)
      setIsDirty(true)
      
      // Validate the updated rig
      const validation = editor.validator.validateRig(updatedData)
      setValidationErrors(validation.errors || [])
      setIsValid(validation.success)
      
    } catch (error) {
      console.error('Error updating block parameter:', error)
      setValidationErrors([{ message: error.message }])
      setIsValid(false)
    }
  }

  // Handle undo
  const handleUndo = () => {
    if (undoStack.length > 0) {
      const previousState = undoStack[undoStack.length - 1]
      setRedoStack(prev => [currentRigData, ...prev])
      setUndoStack(prev => prev.slice(0, -1))
      setCurrentRigData(previousState)
      
      // Check if we're back to original state
      const isBackToOriginal = JSON.stringify(previousState) === JSON.stringify(originalData)
      setIsDirty(!isBackToOriginal)
    }
  }

  // Handle redo
  const handleRedo = () => {
    if (redoStack.length > 0) {
      const nextState = redoStack[0]
      setUndoStack(prev => [...prev, currentRigData])
      setRedoStack(prev => prev.slice(1))
      setCurrentRigData(nextState)
      setIsDirty(true)
    }
  }

  // Handle save
  const handleSave = async () => {
    if (!isValid) {
      alert('Cannot save rig with validation errors')
      return
    }

    setIsSaving(true)
    try {
      const result = await editor.saveRig(rigPath, currentRigData)
      
      if (result.success) {
        setOriginalData(JSON.parse(JSON.stringify(currentRigData)))
        setIsDirty(false)
        setEditMode(false)
        
        if (onSave) {
          onSave(currentRigData, result)
        }
      } else {
        alert(`Failed to save rig: ${result.message}`)
      }
    } catch (error) {
      console.error('Save error:', error)
      alert(`Error saving rig: ${error.message}`)
    } finally {
      setIsSaving(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      handleExitEditMode()
    }
  }

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  // Get filtered blocks for display
  const getFilteredBlocks = () => {
    return blockOrder.filter(block => !['Chain', 'Rig'].includes(block))
  }

  return (
    <Card className={classes.rigCard}>
      <CardContent>
        <Box className={classes.rigHeader}>
          <Typography variant="h5" className={classes.rigName}>
            {shortName}
          </Typography>
          {editMode && (
            <Chip 
              label="EDITING" 
              size="small" 
              className={classes.editModeChip}
            />
          )}
        </Box>

        <Divider />

        {editMode ? (
          <>
            <Tabs value={activeTab} onChange={handleTabChange}>
              <Tab label="Signal Chain" />
              <Tab label="Effect Blocks" />
            </Tabs>

            {activeTab === 0 && (
              <Box className={classes.tabPanel}>
                <SignalChainEditor
                  rigData={currentRigData}
                  onReorder={handleSignalChainReorder}
                  onAddEffect={handleAddEffect}
                  onRemoveEffect={handleRemoveEffect}
                  onEditEffect={handleEditEffect}
                  editMode={editMode}
                  disabled={isSaving}
                />
              </Box>
            )}

            {activeTab === 1 && (
              <Box className={classes.tabPanel}>
                <Box className={classes.blocksSection}>
                  {getFilteredBlocks().map(blockName => (
                    <EditableRigBlock
                      key={blockName}
                      blockName={blockName}
                      blockData={blockChildren[blockName]}
                      onParameterChange={(paramName, newValue) => 
                        handleBlockParameterChange(blockName, paramName, newValue)
                      }
                      editMode={editMode}
                      highlighted={editingBlock === blockName}
                      disabled={isSaving}
                    />
                  ))}
                </Box>
              </Box>
            )}
          </>
        ) : (
          <Box className={classes.signalChainSection}>
            <SignalChainEditor
              rigData={currentRigData}
              editMode={false}
            />
          </Box>
        )}

        {validationErrors.length > 0 && (
          <Box className={classes.validationErrors}>
            {validationErrors.map((error, index) => (
              <Alert key={index} severity="error" size="small">
                {error.message}
              </Alert>
            ))}
          </Box>
        )}
      </CardContent>

      <CardActions className={classes.actionButtons}>
        {editMode ? (
          <>
            <Box className={classes.undoRedoButtons}>
              <Button
                size="small"
                onClick={handleUndo}
                disabled={undoStack.length === 0 || isSaving}
                startIcon={<Undo />}
              >
                Undo
              </Button>
              <Button
                size="small"
                onClick={handleRedo}
                disabled={redoStack.length === 0 || isSaving}
                startIcon={<Redo />}
              >
                Redo
              </Button>
            </Box>
            
            <Button
              size="small"
              onClick={handleCancel}
              disabled={isSaving}
              startIcon={<Cancel />}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              color="primary"
              onClick={handleSave}
              disabled={!isDirty || !isValid || isSaving}
              startIcon={isSaving ? <CircularProgress size={16} /> : <Save />}
            >
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
          </>
        ) : (
          !readOnly && (
            <Button
              size="small"
              variant="outlined"
              onClick={handleEnterEditMode}
              startIcon={<Edit />}
            >
              Edit
            </Button>
          )
        )}
      </CardActions>
    </Card>
  )
}

export default EditableRig
