const { editor } = require('../../../lib/editor')
const debug = require('debug')('hb:api:save-preset')

/**
 * API endpoint for saving preset files
 * POST /api/editor/save-preset
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    })
  }

  try {
    const { filePath, presetData, options = {} } = req.body

    // Validate required fields
    if (!filePath || !presetData) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: filePath and presetData'
      })
    }

    debug(`Saving preset to ${filePath}`)

    // Validate the preset data before saving
    const validation = editor.validator.validatePreset(presetData)
    if (!validation.success) {
      debug(`Preset validation failed: ${validation.errors.length} errors`)
      return res.status(400).json({
        success: false,
        error: 'Preset validation failed',
        validationErrors: validation.errors
      })
    }

    // Save the preset
    const result = await editor.fileManager.savePreset(filePath, presetData, options)

    if (result.success) {
      debug(`Preset saved successfully to ${filePath}`)
      return res.status(200).json(result)
    } else {
      debug(`Failed to save preset: ${result.error}`)
      return res.status(500).json(result)
    }

  } catch (error) {
    debug(`Error in save-preset API: ${error.message}`)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    })
  }
}

// Configure API route
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb', // Allow larger preset files
    },
  },
}
