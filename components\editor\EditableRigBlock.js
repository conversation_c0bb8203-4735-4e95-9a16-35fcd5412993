import React, { useState } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Collapse,
  IconButton,
  Chip
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import { ExpandMore, ExpandLess } from '@material-ui/icons'
import ParameterControl from './ParameterControl'
import { blockColor, suffix } from '../../lib'

const useStyles = makeStyles((theme) => ({
  rigBlock: {
    minWidth: 300,
    maxWidth: 400,
    margin: theme.spacing(1),
    transition: 'all 0.3s ease',
  },
  rigBlockHighlighted: {
    boxShadow: theme.shadows[8],
    borderColor: theme.palette.primary.main,
    borderWidth: 2,
    borderStyle: 'solid',
  },
  blockHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(1),
  },
  blockName: {
    fontWeight: 'bold',
    color: theme.palette.text.primary,
  },
  presetName: {
    fontSize: '0.875rem',
    color: theme.palette.text.secondary,
    fontStyle: 'italic',
    marginBottom: theme.spacing(1),
  },
  parametersContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(1),
  },
  readOnlyParameter: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(0.5),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  parameterName: {
    fontWeight: 'bold',
    fontSize: '0.875rem',
  },
  parameterValue: {
    color: theme.palette.text.secondary,
    fontSize: '0.875rem',
  },
  expandButton: {
    padding: theme.spacing(0.5),
  },
  collapsedInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
    flexWrap: 'wrap',
  },
  parameterChip: {
    fontSize: '0.75rem',
    height: 24,
  }
}))

/**
 * Editable rig block component that can display and edit individual block parameters
 */
const EditableRigBlock = ({
  blockName,
  blockData,
  onParameterChange,
  editMode = false,
  highlighted = false,
  disabled = false
}) => {
  const classes = useStyles()
  const [expanded, setExpanded] = useState(highlighted)

  if (!blockData) return null

  const { childorder, children } = blockData
  
  // Get block color
  const backgroundColor = blockColor(children.Colour)
  
  // Get preset name
  const presetName = children.PresetName?.string || 'Default'
  
  // Filter out system parameters for collapsed view
  const displayParameters = childorder.filter(param => 
    !['PresetName', 'Colour', 'Doubling', 'DoubleStates'].includes(param)
  ).slice(0, 3) // Show only first 3 parameters when collapsed

  // Handle parameter change
  const handleParameterChange = (paramName, newValue) => {
    if (onParameterChange) {
      onParameterChange(paramName, newValue)
    }
  }

  // Render read-only parameter
  const renderReadOnlyParameter = (paramName) => {
    const paramData = children[paramName]
    if (!paramData) return null
    
    const value = paramData.value || paramData.string || paramData.state
    const displayValue = typeof value === 'boolean' ? (value ? 'ON' : 'OFF') : value
    const paramSuffix = suffix(paramName, blockName)
    
    return (
      <Box key={paramName} className={classes.readOnlyParameter}>
        <Typography className={classes.parameterName}>
          {paramName}:
        </Typography>
        <Typography className={classes.parameterValue}>
          {displayValue}{paramSuffix}
        </Typography>
      </Box>
    )
  }

  // Render editable parameter
  const renderEditableParameter = (paramName) => {
    const paramData = children[paramName]
    if (!paramData) return null
    
    const currentValue = paramData.value || paramData.string || paramData.state
    
    return (
      <ParameterControl
        key={paramName}
        paramName={paramName}
        value={currentValue}
        blockType={blockName}
        onChange={handleParameterChange}
        disabled={disabled}
        showValidation={true}
      />
    )
  }

  // Render parameter chip for collapsed view
  const renderParameterChip = (paramName) => {
    const paramData = children[paramName]
    if (!paramData) return null
    
    const value = paramData.value || paramData.string || paramData.state
    const displayValue = typeof value === 'boolean' ? (value ? 'ON' : 'OFF') : value
    const paramSuffix = suffix(paramName, blockName)
    
    return (
      <Chip
        key={paramName}
        size="small"
        label={`${paramName}: ${displayValue}${paramSuffix}`}
        className={classes.parameterChip}
      />
    )
  }

  return (
    <Card 
      className={`${classes.rigBlock} ${highlighted ? classes.rigBlockHighlighted : ''}`}
      style={{ backgroundColor }}
    >
      <CardContent>
        <Box className={classes.blockHeader}>
          <Typography variant="h6" className={classes.blockName}>
            {blockName}
          </Typography>
          <IconButton
            className={classes.expandButton}
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Box>

        <Typography className={classes.presetName}>
          {presetName}
        </Typography>

        <Collapse in={expanded}>
          <Box className={classes.parametersContainer}>
            {childorder
              .filter(param => param !== 'PresetName') // Don't show preset name in parameters
              .map(paramName => 
                editMode 
                  ? renderEditableParameter(paramName)
                  : renderReadOnlyParameter(paramName)
              )}
          </Box>
        </Collapse>

        {!expanded && (
          <Box className={classes.collapsedInfo}>
            {displayParameters.map(paramName => renderParameterChip(paramName))}
            {childorder.length > displayParameters.length + 1 && (
              <Chip
                size="small"
                label={`+${childorder.length - displayParameters.length - 1} more`}
                className={classes.parameterChip}
              />
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default EditableRigBlock
