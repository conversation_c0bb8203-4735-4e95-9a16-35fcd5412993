const debug = require('debug')('hb:lib:editor:core')

/**
 * Core editing functionality for HeadRush presets and rigs
 * Provides safe, validated operations for modifying HeadRush data structures
 */
class EditorCore {
  constructor() {
    this.parameterRanges = {
      // Percentage-based parameters (0-100)
      'Drive': { min: 0, max: 100, type: 'percentage' },
      'Tone': { min: 0, max: 100, type: 'percentage' },
      'Level': { min: 0, max: 100, type: 'percentage' },
      'Bass': { min: 0, max: 100, type: 'percentage' },
      'Mid': { min: 0, max: 100, type: 'percentage' },
      'Treb': { min: 0, max: 100, type: 'percentage' },
      'Master': { min: 0, max: 100, type: 'percentage' },
      'Mix': { min: 0, max: 100, type: 'percentage' },
      'Depth': { min: 0, max: 100, type: 'percentage' },
      'Feedback': { min: 0, max: 100, type: 'percentage' },
      'Volume': { min: 0, max: 100, type: 'percentage' },
      
      // Decibel-based parameters
      'Gain': { min: -20, max: 20, type: 'decibel' },
      'RigVolume': { min: -20, max: 20, type: 'decibel' },
      'Thresh': { min: -60, max: 0, type: 'decibel' },
      'Knee': { min: 0, max: 10, type: 'decibel' },
      
      // Frequency-based parameters (Hz)
      'Freq': { min: 20, max: 20000, type: 'frequency' },
      'HiCut': { min: 1000, max: 20000, type: 'frequency' },
      'LoCut': { min: 20, max: 1000, type: 'frequency' },
      'Rate': { min: 0.1, max: 10, type: 'frequency' },
      
      // Time-based parameters (ms)
      'Release': { min: 1, max: 1000, type: 'time' },
      'PreDelay': { min: 0, max: 500, type: 'time' },
      'Delay': { min: 1, max: 2000, type: 'time' },
      
      // String parameters
      'PresetName': { type: 'string', maxLength: 32 },
      
      // Boolean parameters
      'Doubling': { type: 'boolean' }
    }
  }

  /**
   * Update a parameter in a preset
   * @param {Object} presetData - The preset data structure
   * @param {string} paramName - Name of the parameter to update
   * @param {*} newValue - New value for the parameter
   * @returns {Object} Updated preset data
   */
  updatePresetParameter(presetData, paramName, newValue) {
    debug(`Updating parameter ${paramName} to ${newValue}`)
    
    // Validate the parameter exists
    if (!this.validateParameterExists(presetData, paramName)) {
      throw new Error(`Parameter ${paramName} does not exist in preset`)
    }
    
    // Validate the new value
    if (!this.validateParameterValue(paramName, newValue)) {
      throw new Error(`Invalid value ${newValue} for parameter ${paramName}`)
    }
    
    // Deep clone the preset data to avoid mutations
    const updatedData = JSON.parse(JSON.stringify(presetData))
    const content = JSON.parse(updatedData.content)
    const dataValues = Object.values(content.data)
    const { children } = dataValues[0]
    
    // Update the parameter value
    if (children[paramName]) {
      const paramType = this.getParameterType(paramName, newValue)
      children[paramName] = this.createParameterObject(paramType, newValue)
    }
    
    // Update the content string
    updatedData.content = JSON.stringify(content)
    
    debug(`Parameter ${paramName} updated successfully`)
    return updatedData
  }

  /**
   * Reorder effects in a rig's signal chain
   * @param {Object} rigData - The rig data structure
   * @param {Array} newOrder - New order of effects
   * @returns {Object} Updated rig data
   */
  reorderSignalChain(rigData, newOrder) {
    debug(`Reordering signal chain to: ${newOrder.join(' -> ')}`)
    
    // Validate the rig data structure
    if (!this.validateRigStructure(rigData)) {
      throw new Error('Invalid rig data structure')
    }
    
    // Deep clone the rig data
    const updatedData = JSON.parse(JSON.stringify(rigData))
    const content = JSON.parse(updatedData.content)
    const { data } = content
    const { Patch } = data
    
    // Validate that all blocks in newOrder exist
    const existingBlocks = Patch.childorder
    const missingBlocks = newOrder.filter(block => !existingBlocks.includes(block))
    if (missingBlocks.length > 0) {
      throw new Error(`Missing blocks in new order: ${missingBlocks.join(', ')}`)
    }
    
    // Update the child order
    Patch.childorder = [...newOrder]
    
    // Update the content string
    updatedData.content = JSON.stringify(content)
    
    debug('Signal chain reordered successfully')
    return updatedData
  }

  /**
   * Add an effect to the signal chain
   * @param {Object} rigData - The rig data structure
   * @param {string} effectType - Type of effect to add
   * @param {number} position - Position to insert the effect
   * @returns {Object} Updated rig data
   */
  addEffectToChain(rigData, effectType, position) {
    debug(`Adding ${effectType} effect at position ${position}`)
    
    // Validate inputs
    if (!this.validateRigStructure(rigData)) {
      throw new Error('Invalid rig data structure')
    }
    
    if (!this.isValidEffectType(effectType)) {
      throw new Error(`Invalid effect type: ${effectType}`)
    }
    
    // Deep clone the rig data
    const updatedData = JSON.parse(JSON.stringify(rigData))
    const content = JSON.parse(updatedData.content)
    const { data } = content
    const { Patch } = data
    
    // Insert the effect at the specified position
    const newOrder = [...Patch.childorder]
    newOrder.splice(position, 0, effectType)
    Patch.childorder = newOrder
    
    // Add default effect configuration
    Patch.children[effectType] = this.createDefaultEffectConfig(effectType)
    
    // Update the content string
    updatedData.content = JSON.stringify(content)
    
    debug(`${effectType} effect added successfully`)
    return updatedData
  }

  /**
   * Remove an effect from the signal chain
   * @param {Object} rigData - The rig data structure
   * @param {string} effectId - ID of the effect to remove
   * @returns {Object} Updated rig data
   */
  removeEffectFromChain(rigData, effectId) {
    debug(`Removing effect ${effectId}`)
    
    // Validate inputs
    if (!this.validateRigStructure(rigData)) {
      throw new Error('Invalid rig data structure')
    }
    
    // Deep clone the rig data
    const updatedData = JSON.parse(JSON.stringify(rigData))
    const content = JSON.parse(updatedData.content)
    const { data } = content
    const { Patch } = data
    
    // Remove from child order
    Patch.childorder = Patch.childorder.filter(block => block !== effectId)
    
    // Remove the effect configuration
    delete Patch.children[effectId]
    
    // Update the content string
    updatedData.content = JSON.stringify(content)
    
    debug(`Effect ${effectId} removed successfully`)
    return updatedData
  }

  /**
   * Validate that a parameter exists in the preset
   * @param {Object} presetData - The preset data
   * @param {string} paramName - Parameter name to check
   * @returns {boolean} True if parameter exists
   */
  validateParameterExists(presetData, paramName) {
    try {
      const content = JSON.parse(presetData.content)
      const dataValues = Object.values(content.data)
      const { children } = dataValues[0]
      return children.hasOwnProperty(paramName)
    } catch (error) {
      debug(`Error validating parameter existence: ${error.message}`)
      return false
    }
  }

  /**
   * Validate a parameter value against its constraints
   * @param {string} paramName - Parameter name
   * @param {*} value - Value to validate
   * @returns {boolean} True if value is valid
   */
  validateParameterValue(paramName, value) {
    const paramConfig = this.parameterRanges[paramName]
    if (!paramConfig) {
      // If no specific validation rules, allow the value
      return true
    }
    
    switch (paramConfig.type) {
      case 'percentage':
      case 'decibel':
      case 'frequency':
      case 'time':
        return typeof value === 'number' && 
               value >= paramConfig.min && 
               value <= paramConfig.max
      
      case 'string':
        return typeof value === 'string' && 
               value.length <= (paramConfig.maxLength || 255)
      
      case 'boolean':
        return typeof value === 'boolean'
      
      default:
        return true
    }
  }

  /**
   * Validate the structure of a rig data object
   * @param {Object} rigData - Rig data to validate
   * @returns {boolean} True if structure is valid
   */
  validateRigStructure(rigData) {
    try {
      if (!rigData || !rigData.content) return false
      
      const content = JSON.parse(rigData.content)
      if (!content.data || !content.data.Patch) return false
      
      const { Patch } = content.data
      if (!Patch.childorder || !Array.isArray(Patch.childorder)) return false
      if (!Patch.children || typeof Patch.children !== 'object') return false
      
      return true
    } catch (error) {
      debug(`Rig structure validation error: ${error.message}`)
      return false
    }
  }

  /**
   * Get the appropriate parameter type for a value
   * @param {string} paramName - Parameter name
   * @param {*} value - Parameter value
   * @returns {number} Parameter type code
   */
  getParameterType(paramName, value) {
    if (typeof value === 'boolean') return 1
    return 0 // Default type for numeric and string values
  }

  /**
   * Create a parameter object with the correct structure
   * @param {number} type - Parameter type
   * @param {*} value - Parameter value
   * @returns {Object} Parameter object
   */
  createParameterObject(type, value) {
    const paramObj = { type }
    
    if (type === 1) {
      // Boolean parameter
      paramObj.state = value
    } else if (typeof value === 'string') {
      paramObj.string = value
    } else {
      paramObj.value = value
    }
    
    return paramObj
  }

  /**
   * Check if an effect type is valid
   * @param {string} effectType - Effect type to validate
   * @returns {boolean} True if valid
   */
  isValidEffectType(effectType) {
    const validEffects = [
      'Amp', 'Cab', 'Delay', 'Reverb', 'Chorus', 'Flanger', 
      'Phaser', 'Compressor', 'Distortion', 'Overdrive', 
      'EQ', 'Wah', 'Tremolo', 'Octave'
    ]
    return validEffects.includes(effectType)
  }

  /**
   * Create default configuration for an effect
   * @param {string} effectType - Type of effect
   * @returns {Object} Default effect configuration
   */
  createDefaultEffectConfig(effectType) {
    const baseConfig = {
      childorder: ['PresetName'],
      children: {
        PresetName: { type: 0, string: `Default ${effectType}` }
      }
    }
    
    // Add effect-specific parameters
    switch (effectType) {
      case 'Amp':
        baseConfig.childorder.push('Drive', 'Bass', 'Mid', 'Treb', 'Master')
        baseConfig.children.Drive = { type: 0, value: 50 }
        baseConfig.children.Bass = { type: 0, value: 50 }
        baseConfig.children.Mid = { type: 0, value: 50 }
        baseConfig.children.Treb = { type: 0, value: 50 }
        baseConfig.children.Master = { type: 0, value: 50 }
        break
      
      case 'Delay':
        baseConfig.childorder.push('Delay', 'Feedback', 'Mix')
        baseConfig.children.Delay = { type: 0, value: 250 }
        baseConfig.children.Feedback = { type: 0, value: 25 }
        baseConfig.children.Mix = { type: 0, value: 30 }
        break
      
      // Add more effect types as needed
      default:
        baseConfig.childorder.push('Level')
        baseConfig.children.Level = { type: 0, value: 50 }
    }
    
    return baseConfig
  }
}

module.exports = EditorCore
