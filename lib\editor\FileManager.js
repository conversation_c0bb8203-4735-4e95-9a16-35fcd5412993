const fs = require('fs')
const path = require('path')
const { promisify } = require('util')
const debug = require('debug')('hb:lib:editor:filemanager')

const writeFile = promisify(fs.writeFile)
const readFile = promisify(fs.readFile)
const copyFile = promisify(fs.copyFile)
const mkdir = promisify(fs.mkdir)
const access = promisify(fs.access)
const stat = promisify(fs.stat)

/**
 * File management operations for HeadRush presets and rigs
 * Handles saving, backup, and restore operations with safety checks
 */
class FileManager {
  constructor() {
    this.backupDir = '.headrush-backups'
    this.maxBackups = 10
    this.fileExtensions = {
      preset: '', // Preset files have no extension
      rig: '.rig'
    }
  }

  /**
   * Save a preset file with backup
   * @param {string} filePath - Path to the preset file
   * @param {Object} presetData - Preset data to save
   * @param {Object} options - Save options
   * @returns {Promise<Object>} Save result
   */
  async savePreset(filePath, presetData, options = {}) {
    debug(`Saving preset to ${filePath}`)
    
    try {
      // Validate file path
      this.validateFilePath(filePath, 'preset')
      
      // Create backup if file exists
      let backupPath = null
      if (await this.fileExists(filePath)) {
        backupPath = await this.createBackup(filePath, 'preset')
        debug(`Backup created at ${backupPath}`)
      }
      
      // Validate data structure
      if (!this.validatePresetData(presetData)) {
        throw new Error('Invalid preset data structure')
      }
      
      // Ensure directory exists
      await this.ensureDirectoryExists(path.dirname(filePath))
      
      // Write the file
      const jsonContent = JSON.stringify(presetData, null, options.pretty ? 2 : 0)
      await writeFile(filePath, jsonContent, 'utf8')
      
      debug(`Preset saved successfully to ${filePath}`)
      
      return {
        success: true,
        filePath,
        backupPath,
        message: 'Preset saved successfully'
      }
      
    } catch (error) {
      debug(`Error saving preset: ${error.message}`)
      return {
        success: false,
        error: error.message,
        filePath
      }
    }
  }

  /**
   * Save a rig file with backup
   * @param {string} filePath - Path to the rig file
   * @param {Object} rigData - Rig data to save
   * @param {Object} options - Save options
   * @returns {Promise<Object>} Save result
   */
  async saveRig(filePath, rigData, options = {}) {
    debug(`Saving rig to ${filePath}`)
    
    try {
      // Validate file path
      this.validateFilePath(filePath, 'rig')
      
      // Create backup if file exists
      let backupPath = null
      if (await this.fileExists(filePath)) {
        backupPath = await this.createBackup(filePath, 'rig')
        debug(`Backup created at ${backupPath}`)
      }
      
      // Validate data structure
      if (!this.validateRigData(rigData)) {
        throw new Error('Invalid rig data structure')
      }
      
      // Ensure directory exists
      await this.ensureDirectoryExists(path.dirname(filePath))
      
      // Write the file
      const jsonContent = JSON.stringify(rigData, null, options.pretty ? 2 : 0)
      await writeFile(filePath, jsonContent, 'utf8')
      
      debug(`Rig saved successfully to ${filePath}`)
      
      return {
        success: true,
        filePath,
        backupPath,
        message: 'Rig saved successfully'
      }
      
    } catch (error) {
      debug(`Error saving rig: ${error.message}`)
      return {
        success: false,
        error: error.message,
        filePath
      }
    }
  }

  /**
   * Create a backup of an existing file
   * @param {string} originalPath - Path to the original file
   * @param {string} fileType - Type of file ('preset' or 'rig')
   * @returns {Promise<string>} Path to the backup file
   */
  async createBackup(originalPath, fileType) {
    debug(`Creating backup for ${originalPath}`)
    
    try {
      // Ensure backup directory exists
      const backupDir = path.join(path.dirname(originalPath), this.backupDir)
      await this.ensureDirectoryExists(backupDir)
      
      // Generate backup filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const originalName = path.basename(originalPath)
      const backupName = `${originalName}.backup.${timestamp}`
      const backupPath = path.join(backupDir, backupName)
      
      // Copy the file
      await copyFile(originalPath, backupPath)
      
      // Clean up old backups
      await this.cleanupOldBackups(backupDir, originalName)
      
      debug(`Backup created successfully at ${backupPath}`)
      return backupPath
      
    } catch (error) {
      debug(`Error creating backup: ${error.message}`)
      throw new Error(`Failed to create backup: ${error.message}`)
    }
  }

  /**
   * Restore a file from backup
   * @param {string} backupPath - Path to the backup file
   * @param {string} targetPath - Path where to restore the file
   * @returns {Promise<Object>} Restore result
   */
  async restoreFromBackup(backupPath, targetPath) {
    debug(`Restoring from backup ${backupPath} to ${targetPath}`)
    
    try {
      // Validate backup file exists
      if (!await this.fileExists(backupPath)) {
        throw new Error('Backup file does not exist')
      }
      
      // Create backup of current file before restore
      let currentBackupPath = null
      if (await this.fileExists(targetPath)) {
        currentBackupPath = await this.createBackup(targetPath, 'restore')
      }
      
      // Copy backup to target location
      await copyFile(backupPath, targetPath)
      
      debug(`File restored successfully from ${backupPath}`)
      
      return {
        success: true,
        restoredFrom: backupPath,
        restoredTo: targetPath,
        currentBackup: currentBackupPath,
        message: 'File restored successfully'
      }
      
    } catch (error) {
      debug(`Error restoring from backup: ${error.message}`)
      return {
        success: false,
        error: error.message,
        backupPath,
        targetPath
      }
    }
  }

  /**
   * List available backups for a file
   * @param {string} originalPath - Path to the original file
   * @returns {Promise<Array>} List of backup files
   */
  async listBackups(originalPath) {
    debug(`Listing backups for ${originalPath}`)
    
    try {
      const backupDir = path.join(path.dirname(originalPath), this.backupDir)
      const originalName = path.basename(originalPath)
      
      if (!await this.fileExists(backupDir)) {
        return []
      }
      
      const fs = require('fs')
      const readdir = promisify(fs.readdir)
      const files = await readdir(backupDir)
      
      // Filter backup files for this original file
      const backupFiles = files
        .filter(file => file.startsWith(`${originalName}.backup.`))
        .map(file => ({
          filename: file,
          path: path.join(backupDir, file),
          timestamp: this.extractTimestampFromBackup(file)
        }))
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      
      debug(`Found ${backupFiles.length} backups`)
      return backupFiles
      
    } catch (error) {
      debug(`Error listing backups: ${error.message}`)
      return []
    }
  }

  /**
   * Validate file path for security and correctness
   * @param {string} filePath - File path to validate
   * @param {string} fileType - Expected file type
   */
  validateFilePath(filePath, fileType) {
    if (!filePath || typeof filePath !== 'string') {
      throw new Error('Invalid file path')
    }
    
    // Prevent directory traversal attacks
    if (filePath.includes('..') || filePath.includes('~')) {
      throw new Error('Invalid file path: directory traversal not allowed')
    }
    
    // Check file extension
    const expectedExt = this.fileExtensions[fileType]
    if (expectedExt && !filePath.endsWith(expectedExt)) {
      throw new Error(`Invalid file extension for ${fileType}`)
    }
    
    // Ensure path is within allowed directories
    const normalizedPath = path.normalize(filePath)
    if (!normalizedPath.includes('/Blocks/') && !normalizedPath.includes('/Rigs/')) {
      throw new Error('File must be in Blocks or Rigs directory')
    }
  }

  /**
   * Validate preset data structure
   * @param {Object} presetData - Preset data to validate
   * @returns {boolean} True if valid
   */
  validatePresetData(presetData) {
    if (!presetData || typeof presetData !== 'object') {
      return false
    }
    
    if (!presetData.content || typeof presetData.content !== 'string') {
      return false
    }
    
    try {
      const content = JSON.parse(presetData.content)
      return content && content.data && typeof content.data === 'object'
    } catch (error) {
      return false
    }
  }

  /**
   * Validate rig data structure
   * @param {Object} rigData - Rig data to validate
   * @returns {boolean} True if valid
   */
  validateRigData(rigData) {
    if (!rigData || typeof rigData !== 'object') {
      return false
    }
    
    if (!rigData.content || typeof rigData.content !== 'string') {
      return false
    }
    
    try {
      const content = JSON.parse(rigData.content)
      return content && content.data && content.data.Patch && typeof content.data.Patch === 'object'
    } catch (error) {
      return false
    }
  }

  /**
   * Check if a file exists
   * @param {string} filePath - Path to check
   * @returns {Promise<boolean>} True if file exists
   */
  async fileExists(filePath) {
    try {
      await access(filePath, fs.constants.F_OK)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Ensure a directory exists, create if it doesn't
   * @param {string} dirPath - Directory path
   */
  async ensureDirectoryExists(dirPath) {
    try {
      await access(dirPath, fs.constants.F_OK)
    } catch (error) {
      await mkdir(dirPath, { recursive: true })
      debug(`Created directory ${dirPath}`)
    }
  }

  /**
   * Clean up old backup files, keeping only the most recent ones
   * @param {string} backupDir - Backup directory path
   * @param {string} originalName - Original filename
   */
  async cleanupOldBackups(backupDir, originalName) {
    try {
      const backups = await this.listBackups(path.join(path.dirname(backupDir), originalName))
      
      if (backups.length > this.maxBackups) {
        const fs = require('fs')
        const unlink = promisify(fs.unlink)
        
        // Remove oldest backups
        const toRemove = backups.slice(this.maxBackups)
        for (const backup of toRemove) {
          await unlink(backup.path)
          debug(`Removed old backup ${backup.filename}`)
        }
      }
    } catch (error) {
      debug(`Error cleaning up old backups: ${error.message}`)
      // Non-critical error, don't throw
    }
  }

  /**
   * Extract timestamp from backup filename
   * @param {string} filename - Backup filename
   * @returns {string} ISO timestamp
   */
  extractTimestampFromBackup(filename) {
    const match = filename.match(/\.backup\.(.+)$/)
    if (match) {
      return match[1].replace(/-/g, ':').replace(/T(\d{2})-(\d{2})-(\d{2})/, 'T$1:$2:$3')
    }
    return new Date().toISOString()
  }
}

module.exports = FileManager
