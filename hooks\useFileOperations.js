import { useState, useCallback } from 'react'

/**
 * Custom hook for file operations (save, backup, validate)
 * Provides client-side interface to the editor API endpoints
 */
const useFileOperations = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  // Clear error state
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Generic API call helper
  const apiCall = useCallback(async (endpoint, options = {}) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        ...options
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`)
      }

      return result
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Save preset
  const savePreset = useCallback(async (filePath, presetData, options = {}) => {
    return await apiCall('/api/editor/save-preset', {
      body: JSON.stringify({
        filePath,
        presetData,
        options
      })
    })
  }, [apiCall])

  // Save rig
  const saveRig = useCallback(async (filePath, rigData, options = {}) => {
    return await apiCall('/api/editor/save-rig', {
      body: JSON.stringify({
        filePath,
        rigData,
        options
      })
    })
  }, [apiCall])

  // Validate data
  const validate = useCallback(async (data, type) => {
    return await apiCall('/api/editor/validate', {
      body: JSON.stringify({
        data,
        type
      })
    })
  }, [apiCall])

  // Create backup
  const createBackup = useCallback(async (filePath, fileType) => {
    return await apiCall('/api/editor/backup', {
      body: JSON.stringify({
        filePath,
        fileType
      })
    })
  }, [apiCall])

  // List backups
  const listBackups = useCallback(async (filePath) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/editor/backup?filePath=${encodeURIComponent(filePath)}`, {
        method: 'GET'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`)
      }

      return result
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Batch save multiple files
  const batchSave = useCallback(async (operations) => {
    setIsLoading(true)
    setError(null)

    const results = []
    const errors = []

    try {
      for (const operation of operations) {
        try {
          let result
          if (operation.type === 'preset') {
            result = await savePreset(operation.filePath, operation.data, operation.options)
          } else if (operation.type === 'rig') {
            result = await saveRig(operation.filePath, operation.data, operation.options)
          } else {
            throw new Error(`Unknown operation type: ${operation.type}`)
          }
          results.push({ ...operation, result, success: true })
        } catch (error) {
          errors.push({ ...operation, error: error.message, success: false })
        }
      }

      if (errors.length > 0) {
        const errorMessage = `${errors.length} of ${operations.length} operations failed`
        setError(errorMessage)
      }

      return {
        success: errors.length === 0,
        results,
        errors,
        totalOperations: operations.length,
        successfulOperations: results.length,
        failedOperations: errors.length
      }

    } catch (error) {
      setError(error.message)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [savePreset, saveRig])

  // Auto-save functionality
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(false)
  const [autoSaveInterval, setAutoSaveInterval] = useState(30000) // 30 seconds

  const enableAutoSave = useCallback((interval = 30000) => {
    setAutoSaveEnabled(true)
    setAutoSaveInterval(interval)
  }, [])

  const disableAutoSave = useCallback(() => {
    setAutoSaveEnabled(false)
  }, [])

  // Auto-save implementation would require additional state management
  // This is a placeholder for the auto-save functionality
  const autoSave = useCallback(async (filePath, data, type) => {
    if (!autoSaveEnabled) return

    try {
      if (type === 'preset') {
        return await savePreset(filePath, data, { autoSave: true })
      } else if (type === 'rig') {
        return await saveRig(filePath, data, { autoSave: true })
      }
    } catch (error) {
      // Auto-save errors should be handled gracefully
      console.warn('Auto-save failed:', error.message)
    }
  }, [autoSaveEnabled, savePreset, saveRig])

  return {
    // State
    isLoading,
    error,
    autoSaveEnabled,
    autoSaveInterval,

    // File operations
    savePreset,
    saveRig,
    validate,
    createBackup,
    listBackups,
    batchSave,

    // Auto-save
    enableAutoSave,
    disableAutoSave,
    autoSave,

    // Utilities
    clearError
  }
}

export default useFileOperations
