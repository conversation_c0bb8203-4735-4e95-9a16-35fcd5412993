import React from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Switch,
  FormControlLabel,
  Box,
  Chip,
  IconButton,
  Tooltip
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import {
  Edit,
  Save,
  Cancel,
  Undo,
  Redo,
  Backup,
  Warning,
  CheckCircle,
  Error
} from '@material-ui/icons'

const useStyles = makeStyles((theme) => ({
  toolbar: {
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  title: {
    flexGrow: 1,
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
  },
  editModeSwitch: {
    marginRight: theme.spacing(2),
  },
  statusChip: {
    marginLeft: theme.spacing(1),
  },
  dirtyChip: {
    backgroundColor: theme.palette.warning.light,
    color: theme.palette.warning.contrastText,
  },
  validChip: {
    backgroundColor: theme.palette.success.light,
    color: theme.palette.success.contrastText,
  },
  invalidChip: {
    backgroundColor: theme.palette.error.light,
    color: theme.palette.error.contrastText,
  },
  actionButtons: {
    display: 'flex',
    gap: theme.spacing(1),
    alignItems: 'center',
  },
  undoRedoGroup: {
    display: 'flex',
    gap: theme.spacing(0.5),
    marginRight: theme.spacing(1),
  }
}))

/**
 * Editor toolbar component providing global editing controls
 */
const EditorToolbar = ({
  editMode = false,
  onEditModeChange,
  isDirty = false,
  isValid = true,
  validationErrors = [],
  canUndo = false,
  canRedo = false,
  onUndo,
  onRedo,
  onSave,
  onCancel,
  onCreateBackup,
  isSaving = false,
  title = "HeadRush Editor"
}) => {
  const classes = useStyles()

  // Handle edit mode toggle
  const handleEditModeToggle = (event) => {
    const newEditMode = event.target.checked
    
    if (!newEditMode && isDirty) {
      const confirmExit = window.confirm(
        'You have unsaved changes. Are you sure you want to exit edit mode?'
      )
      if (!confirmExit) return
    }
    
    if (onEditModeChange) {
      onEditModeChange(newEditMode)
    }
  }

  // Get status chip based on current state
  const getStatusChip = () => {
    if (!editMode) {
      return (
        <Chip
          size="small"
          label="READ ONLY"
          icon={<CheckCircle />}
          className={classes.statusChip}
        />
      )
    }

    if (!isValid) {
      return (
        <Tooltip title={`${validationErrors.length} validation error(s)`}>
          <Chip
            size="small"
            label="INVALID"
            icon={<Error />}
            className={`${classes.statusChip} ${classes.invalidChip}`}
          />
        </Tooltip>
      )
    }

    if (isDirty) {
      return (
        <Chip
          size="small"
          label="MODIFIED"
          icon={<Warning />}
          className={`${classes.statusChip} ${classes.dirtyChip}`}
        />
      )
    }

    return (
      <Chip
        size="small"
        label="EDITING"
        icon={<Edit />}
        className={`${classes.statusChip} ${classes.validChip}`}
      />
    )
  }

  return (
    <AppBar position="static" elevation={1} className={classes.toolbar}>
      <Toolbar>
        <Box className={classes.title}>
          <Typography variant="h6">
            {title}
          </Typography>
          {getStatusChip()}
        </Box>

        <Box className={classes.actionButtons}>
          {/* Edit Mode Toggle */}
          <FormControlLabel
            control={
              <Switch
                checked={editMode}
                onChange={handleEditModeToggle}
                color="primary"
              />
            }
            label="Edit Mode"
            className={classes.editModeSwitch}
          />

          {/* Undo/Redo Controls */}
          {editMode && (
            <Box className={classes.undoRedoGroup}>
              <Tooltip title="Undo last change">
                <span>
                  <IconButton
                    size="small"
                    onClick={onUndo}
                    disabled={!canUndo || isSaving}
                  >
                    <Undo />
                  </IconButton>
                </span>
              </Tooltip>
              <Tooltip title="Redo last undone change">
                <span>
                  <IconButton
                    size="small"
                    onClick={onRedo}
                    disabled={!canRedo || isSaving}
                  >
                    <Redo />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          )}

          {/* Backup Button */}
          <Tooltip title="Create backup of current files">
            <IconButton
              size="small"
              onClick={onCreateBackup}
              disabled={isSaving}
            >
              <Backup />
            </IconButton>
          </Tooltip>

          {/* Save/Cancel Buttons */}
          {editMode && (
            <>
              <Button
                size="small"
                onClick={onCancel}
                disabled={isSaving}
                startIcon={<Cancel />}
              >
                Cancel
              </Button>
              <Button
                size="small"
                variant="contained"
                color="primary"
                onClick={onSave}
                disabled={!isDirty || !isValid || isSaving}
                startIcon={<Save />}
              >
                {isSaving ? 'Saving...' : 'Save All'}
              </Button>
            </>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  )
}

export default EditorToolbar
