import { useState, useCallback, useRef, useEffect } from 'react'
import { editor } from '../lib/editor'

/**
 * Custom hook for managing editor state across the application
 * Provides centralized state management for editing operations
 */
const useEditorState = () => {
  // Core editor state
  const [editMode, setEditMode] = useState(false)
  const [isDirty, setIsDirty] = useState(false)
  const [isValid, setIsValid] = useState(true)
  const [validationErrors, setValidationErrors] = useState([])
  const [isSaving, setIsSaving] = useState(false)

  // Undo/Redo state
  const [undoStack, setUndoStack] = useState([])
  const [redoStack, setRedoStack] = useState([])
  const maxUndoSteps = 50

  // Current data state
  const [currentData, setCurrentData] = useState(null)
  const [originalData, setOriginalData] = useState(null)
  const originalDataRef = useRef(null)

  // File tracking
  const [currentFilePath, setCurrentFilePath] = useState(null)
  const [currentFileType, setCurrentFileType] = useState(null) // 'preset' or 'rig'

  // Initialize editing session
  const initializeEditor = useCallback((data, filePath, fileType) => {
    setCurrentData(data)
    setOriginalData(JSON.parse(JSON.stringify(data)))
    originalDataRef.current = JSON.parse(JSON.stringify(data))
    setCurrentFilePath(filePath)
    setCurrentFileType(fileType)
    setIsDirty(false)
    setIsValid(true)
    setValidationErrors([])
    setUndoStack([])
    setRedoStack([])
  }, [])

  // Enter edit mode
  const enterEditMode = useCallback(() => {
    if (!currentData) {
      console.warn('No data loaded for editing')
      return false
    }
    
    setEditMode(true)
    setUndoStack([])
    setRedoStack([])
    return true
  }, [currentData])

  // Exit edit mode
  const exitEditMode = useCallback((force = false) => {
    if (isDirty && !force) {
      const confirmExit = window.confirm(
        'You have unsaved changes. Are you sure you want to exit edit mode?'
      )
      if (!confirmExit) return false
    }

    setEditMode(false)
    setCurrentData(originalDataRef.current)
    setIsDirty(false)
    setIsValid(true)
    setValidationErrors([])
    setUndoStack([])
    setRedoStack([])
    return true
  }, [isDirty])

  // Add state to undo stack
  const addToUndoStack = useCallback((state) => {
    setUndoStack(prev => {
      const newStack = [...prev, state]
      // Limit undo stack size
      if (newStack.length > maxUndoSteps) {
        return newStack.slice(-maxUndoSteps)
      }
      return newStack
    })
    setRedoStack([]) // Clear redo stack on new change
  }, [maxUndoSteps])

  // Update data with validation
  const updateData = useCallback((newData, skipValidation = false) => {
    if (!editMode) {
      console.warn('Cannot update data outside of edit mode')
      return false
    }

    // Add current state to undo stack
    addToUndoStack(JSON.parse(JSON.stringify(currentData)))

    // Update current data
    setCurrentData(newData)

    // Check if data is dirty
    const isDataDirty = JSON.stringify(newData) !== JSON.stringify(originalDataRef.current)
    setIsDirty(isDataDirty)

    // Validate if not skipping
    if (!skipValidation) {
      const validation = editor.validate(newData, currentFileType)
      setIsValid(validation.success)
      setValidationErrors(validation.errors || [])
    }

    return true
  }, [editMode, currentData, currentFileType, addToUndoStack])

  // Undo last change
  const undo = useCallback(() => {
    if (undoStack.length === 0) return false

    const previousState = undoStack[undoStack.length - 1]
    
    // Add current state to redo stack
    setRedoStack(prev => [JSON.parse(JSON.stringify(currentData)), ...prev])
    
    // Remove last item from undo stack
    setUndoStack(prev => prev.slice(0, -1))
    
    // Restore previous state
    setCurrentData(previousState)
    
    // Update dirty state
    const isDataDirty = JSON.stringify(previousState) !== JSON.stringify(originalDataRef.current)
    setIsDirty(isDataDirty)
    
    // Revalidate
    const validation = editor.validate(previousState, currentFileType)
    setIsValid(validation.success)
    setValidationErrors(validation.errors || [])
    
    return true
  }, [undoStack, currentData, currentFileType])

  // Redo last undone change
  const redo = useCallback(() => {
    if (redoStack.length === 0) return false

    const nextState = redoStack[0]
    
    // Add current state to undo stack
    addToUndoStack(JSON.parse(JSON.stringify(currentData)))
    
    // Remove first item from redo stack
    setRedoStack(prev => prev.slice(1))
    
    // Restore next state
    setCurrentData(nextState)
    
    // Update dirty state
    setIsDirty(true) // Redo always makes data dirty
    
    // Revalidate
    const validation = editor.validate(nextState, currentFileType)
    setIsValid(validation.success)
    setValidationErrors(validation.errors || [])
    
    return true
  }, [redoStack, currentData, currentFileType, addToUndoStack])

  // Save current data
  const save = useCallback(async (options = {}) => {
    if (!currentData || !currentFilePath || !currentFileType) {
      return { success: false, error: 'No data to save' }
    }

    if (!isValid) {
      return { success: false, error: 'Cannot save invalid data' }
    }

    setIsSaving(true)
    
    try {
      let result
      if (currentFileType === 'preset') {
        result = await editor.savePreset(currentFilePath, currentData, options)
      } else if (currentFileType === 'rig') {
        result = await editor.saveRig(currentFilePath, currentData, options)
      } else {
        throw new Error('Unknown file type')
      }

      if (result.success) {
        // Update original data reference
        originalDataRef.current = JSON.parse(JSON.stringify(currentData))
        setOriginalData(JSON.parse(JSON.stringify(currentData)))
        setIsDirty(false)
      }

      return result
    } catch (error) {
      return { success: false, error: error.message }
    } finally {
      setIsSaving(false)
    }
  }, [currentData, currentFilePath, currentFileType, isValid])

  // Create backup
  const createBackup = useCallback(async () => {
    if (!currentFilePath || !currentFileType) {
      return { success: false, error: 'No file to backup' }
    }

    try {
      const result = await editor.createBackup(currentFilePath, currentFileType)
      return result
    } catch (error) {
      return { success: false, error: error.message }
    }
  }, [currentFilePath, currentFileType])

  // Reset editor state
  const reset = useCallback(() => {
    setEditMode(false)
    setIsDirty(false)
    setIsValid(true)
    setValidationErrors([])
    setIsSaving(false)
    setUndoStack([])
    setRedoStack([])
    setCurrentData(null)
    setOriginalData(null)
    originalDataRef.current = null
    setCurrentFilePath(null)
    setCurrentFileType(null)
  }, [])

  // Computed values
  const canUndo = undoStack.length > 0
  const canRedo = redoStack.length > 0
  const hasUnsavedChanges = isDirty
  const canSave = editMode && isDirty && isValid && !isSaving

  return {
    // State
    editMode,
    isDirty,
    isValid,
    validationErrors,
    isSaving,
    currentData,
    originalData,
    currentFilePath,
    currentFileType,
    canUndo,
    canRedo,
    hasUnsavedChanges,
    canSave,

    // Actions
    initializeEditor,
    enterEditMode,
    exitEditMode,
    updateData,
    undo,
    redo,
    save,
    createBackup,
    reset,

    // Utilities
    addToUndoStack
  }
}

export default useEditorState
