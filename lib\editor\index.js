/**
 * HeadRush Editor Library
 * 
 * Core editing functionality for HeadRush presets and rigs
 * Provides safe, validated operations for modifying HeadRush data structures
 */

const EditorCore = require('./EditorCore')
const ValidationEngine = require('./ValidationEngine')
const FileManager = require('./FileManager')

// Create singleton instances
const editorCore = new EditorCore()
const validationEngine = new ValidationEngine()
const fileManager = new FileManager()

/**
 * High-level editing operations that combine core functionality
 */
class HeadRushEditor {
  constructor() {
    this.core = editorCore
    this.validator = validationEngine
    this.fileManager = fileManager
  }

  /**
   * Update a preset parameter with full validation and backup
   * @param {string} presetPath - Path to the preset file
   * @param {Object} presetData - Current preset data
   * @param {string} paramName - Parameter name to update
   * @param {*} newValue - New parameter value
   * @returns {Promise<Object>} Update result
   */
  async updatePresetParameter(presetPath, presetData, paramName, newValue) {
    try {
      // Validate the parameter change
      const validation = this.validator.validateParameterValue(paramName, newValue)
      if (!validation.success) {
        return {
          success: false,
          errors: validation.errors,
          message: 'Parameter validation failed'
        }
      }

      // Apply the change
      const updatedData = this.core.updatePresetParameter(presetData, paramName, newValue)

      // Validate the complete updated preset
      const presetValidation = this.validator.validatePreset(updatedData)
      if (!presetValidation.success) {
        return {
          success: false,
          errors: presetValidation.errors,
          message: 'Updated preset validation failed'
        }
      }

      return {
        success: true,
        data: updatedData,
        message: `Parameter ${paramName} updated successfully`
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to update preset parameter'
      }
    }
  }

  /**
   * Save a preset with full validation and backup
   * @param {string} presetPath - Path to save the preset
   * @param {Object} presetData - Preset data to save
   * @param {Object} options - Save options
   * @returns {Promise<Object>} Save result
   */
  async savePreset(presetPath, presetData, options = {}) {
    try {
      // For client-side usage, use API endpoint
      if (typeof window !== 'undefined') {
        const response = await fetch('/api/editor/save-preset', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ filePath: presetPath, presetData, options })
        })
        return await response.json()
      }

      // For server-side usage, use direct file operations
      const validation = this.validator.validatePreset(presetData)
      if (!validation.success) {
        return {
          success: false,
          errors: validation.errors,
          message: 'Preset validation failed before save'
        }
      }

      const saveResult = await this.fileManager.savePreset(presetPath, presetData, options)
      return saveResult

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to save preset'
      }
    }
  }

  /**
   * Update rig signal chain with validation
   * @param {string} rigPath - Path to the rig file
   * @param {Object} rigData - Current rig data
   * @param {Array} newOrder - New signal chain order
   * @returns {Promise<Object>} Update result
   */
  async updateRigSignalChain(rigPath, rigData, newOrder) {
    try {
      // Apply the change
      const updatedData = this.core.reorderSignalChain(rigData, newOrder)

      // Validate the complete updated rig
      const rigValidation = this.validator.validateRig(updatedData)
      if (!rigValidation.success) {
        return {
          success: false,
          errors: rigValidation.errors,
          message: 'Updated rig validation failed'
        }
      }

      return {
        success: true,
        data: updatedData,
        message: 'Signal chain updated successfully'
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to update signal chain'
      }
    }
  }

  /**
   * Add effect to rig with validation
   * @param {string} rigPath - Path to the rig file
   * @param {Object} rigData - Current rig data
   * @param {string} effectType - Type of effect to add
   * @param {number} position - Position to insert the effect
   * @returns {Promise<Object>} Update result
   */
  async addEffectToRig(rigPath, rigData, effectType, position) {
    try {
      // Apply the change
      const updatedData = this.core.addEffectToChain(rigData, effectType, position)

      // Validate the complete updated rig
      const rigValidation = this.validator.validateRig(updatedData)
      if (!rigValidation.success) {
        return {
          success: false,
          errors: rigValidation.errors,
          message: 'Updated rig validation failed'
        }
      }

      return {
        success: true,
        data: updatedData,
        message: `${effectType} effect added successfully`
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to add effect'
      }
    }
  }

  /**
   * Remove effect from rig with validation
   * @param {string} rigPath - Path to the rig file
   * @param {Object} rigData - Current rig data
   * @param {string} effectId - ID of effect to remove
   * @returns {Promise<Object>} Update result
   */
  async removeEffectFromRig(rigPath, rigData, effectId) {
    try {
      // Apply the change
      const updatedData = this.core.removeEffectFromChain(rigData, effectId)

      // Validate the complete updated rig
      const rigValidation = this.validator.validateRig(updatedData)
      if (!rigValidation.success) {
        return {
          success: false,
          errors: rigValidation.errors,
          message: 'Updated rig validation failed'
        }
      }

      return {
        success: true,
        data: updatedData,
        message: `Effect ${effectId} removed successfully`
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to remove effect'
      }
    }
  }

  /**
   * Save a rig with full validation and backup
   * @param {string} rigPath - Path to save the rig
   * @param {Object} rigData - Rig data to save
   * @param {Object} options - Save options
   * @returns {Promise<Object>} Save result
   */
  async saveRig(rigPath, rigData, options = {}) {
    try {
      // For client-side usage, use API endpoint
      if (typeof window !== 'undefined') {
        const response = await fetch('/api/editor/save-rig', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ filePath: rigPath, rigData, options })
        })
        return await response.json()
      }

      // For server-side usage, use direct file operations
      const validation = this.validator.validateRig(rigData)
      if (!validation.success) {
        return {
          success: false,
          errors: validation.errors,
          message: 'Rig validation failed before save'
        }
      }

      const saveResult = await this.fileManager.saveRig(rigPath, rigData, options)
      return saveResult

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to save rig'
      }
    }
  }

  /**
   * Create backup of a file
   * @param {string} filePath - Path to the file
   * @param {string} fileType - Type of file ('preset' or 'rig')
   * @returns {Promise<Object>} Backup result
   */
  async createBackup(filePath, fileType) {
    try {
      const backupPath = await this.fileManager.createBackup(filePath, fileType)
      return {
        success: true,
        backupPath,
        message: 'Backup created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to create backup'
      }
    }
  }

  /**
   * Restore file from backup
   * @param {string} backupPath - Path to backup file
   * @param {string} targetPath - Target path for restore
   * @returns {Promise<Object>} Restore result
   */
  async restoreFromBackup(backupPath, targetPath) {
    return await this.fileManager.restoreFromBackup(backupPath, targetPath)
  }

  /**
   * List available backups for a file
   * @param {string} filePath - Original file path
   * @returns {Promise<Array>} List of backups
   */
  async listBackups(filePath) {
    return await this.fileManager.listBackups(filePath)
  }

  /**
   * Validate a preset or rig file
   * @param {Object} data - Data to validate
   * @param {string} type - Type of data ('preset' or 'rig')
   * @returns {Object} Validation result
   */
  validate(data, type) {
    if (type === 'preset') {
      return this.validator.validatePreset(data)
    } else if (type === 'rig') {
      return this.validator.validateRig(data)
    } else {
      return {
        success: false,
        errors: [{ type: 'INVALID_TYPE', message: 'Invalid data type specified' }]
      }
    }
  }
}

// Create and export singleton instance
const headRushEditor = new HeadRushEditor()

module.exports = {
  HeadRushEditor,
  EditorCore,
  ValidationEngine,
  FileManager,
  // Export singleton for convenience
  editor: headRushEditor,
  core: editorCore,
  validator: validationEngine,
  fileManager: fileManager
}
