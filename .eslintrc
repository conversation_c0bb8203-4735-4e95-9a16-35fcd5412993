{"extends": ["eslint:recommended", "plugin:flowtype/recommended", "plugin:jest/recommended", "plugin:react/recommended", "plugin:jsx-a11y/recommended", "plugin:import/warnings", "plugin:import/errors", "prettier", "prettier/flowtype", "prettier/react"], "plugins": ["flowtype", "react", "prettier", "jsx-a11y", "import", "react-hooks"], "parser": "babel-es<PERSON>", "parserOptions": {"ecmaVersion": 2018, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"es6": true, "node": true, "jest": true, "browser": true}, "rules": {"prettier/prettier": "warn", "react/display-name": "off", "eqeqeq": "warn", "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/label-has-associated-control": "error", "jsx-a11y/label-has-for": "off", "jsx-a11y/no-onchange": "off", "jsx-a11y/media-has-caption": "off", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always-and-inside-groups"}], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "settings": {"import/resolver": {"babel-module": {"root": ["./"]}}, "react": {"version": "detect"}}}