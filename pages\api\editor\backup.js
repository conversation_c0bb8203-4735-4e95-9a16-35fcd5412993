const { editor } = require('../../../lib/editor')
const debug = require('debug')('hb:api:backup')

/**
 * API endpoint for backup operations
 * POST /api/editor/backup - Create backup
 * GET /api/editor/backup - List backups
 */
export default async function handler(req, res) {
  try {
    if (req.method === 'POST') {
      return await handleCreateBackup(req, res)
    } else if (req.method === 'GET') {
      return await handleListBackups(req, res)
    } else {
      return res.status(405).json({ 
        success: false, 
        error: 'Method not allowed' 
      })
    }
  } catch (error) {
    debug(`Error in backup API: ${error.message}`)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    })
  }
}

/**
 * Handle backup creation
 */
async function handleCreateBackup(req, res) {
  const { filePath, fileType } = req.body

  // Validate required fields
  if (!filePath || !fileType) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields: filePath and fileType'
    })
  }

  // Validate file type
  if (!['preset', 'rig'].includes(fileType)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid fileType. Must be "preset" or "rig"'
    })
  }

  debug(`Creating backup for ${filePath}`)

  try {
    const backupPath = await editor.fileManager.createBackup(filePath, fileType)
    
    debug(`Backup created successfully at ${backupPath}`)
    return res.status(200).json({
      success: true,
      backupPath,
      message: 'Backup created successfully'
    })

  } catch (error) {
    debug(`Failed to create backup: ${error.message}`)
    return res.status(500).json({
      success: false,
      error: 'Failed to create backup',
      details: error.message
    })
  }
}

/**
 * Handle listing backups
 */
async function handleListBackups(req, res) {
  const { filePath } = req.query

  // Validate required fields
  if (!filePath) {
    return res.status(400).json({
      success: false,
      error: 'Missing required parameter: filePath'
    })
  }

  debug(`Listing backups for ${filePath}`)

  try {
    const backups = await editor.fileManager.listBackups(filePath)
    
    debug(`Found ${backups.length} backups`)
    return res.status(200).json({
      success: true,
      backups,
      count: backups.length
    })

  } catch (error) {
    debug(`Failed to list backups: ${error.message}`)
    return res.status(500).json({
      success: false,
      error: 'Failed to list backups',
      details: error.message
    })
  }
}
