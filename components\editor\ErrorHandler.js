import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import {
  Error,
  Warning,
  Info,
  ExpandMore,
  ExpandLess,
  BugReport,
  Refresh,
  GetApp
} from '@material-ui/icons'

const useStyles = makeStyles((theme) => ({
  errorContainer: {
    margin: theme.spacing(1, 0),
  },
  errorDetails: {
    marginTop: theme.spacing(1),
  },
  errorList: {
    maxHeight: 200,
    overflow: 'auto',
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  errorItem: {
    borderBottom: `1px solid ${theme.palette.divider}`,
    '&:last-child': {
      borderBottom: 'none',
    }
  },
  errorActions: {
    display: 'flex',
    gap: theme.spacing(1),
    marginTop: theme.spacing(1),
  },
  severityChip: {
    fontSize: '0.75rem',
    height: 20,
  },
  errorCode: {
    fontFamily: 'monospace',
    fontSize: '0.875rem',
    backgroundColor: theme.palette.grey[100],
    padding: theme.spacing(0.5),
    borderRadius: theme.shape.borderRadius,
  }
}))

/**
 * Comprehensive error handling component for the HeadRush Editor
 */
const ErrorHandler = ({
  errors = [],
  onRetry,
  onDismiss,
  onReportBug,
  showDetails = false,
  title = "Validation Errors"
}) => {
  const classes = useStyles()
  const [expanded, setExpanded] = React.useState(showDetails)
  const [reportDialogOpen, setReportDialogOpen] = React.useState(false)

  if (!errors || errors.length === 0) {
    return null
  }

  // Categorize errors by severity
  const categorizedErrors = {
    error: errors.filter(err => err.severity === 'error' || !err.severity),
    warning: errors.filter(err => err.severity === 'warning'),
    info: errors.filter(err => err.severity === 'info')
  }

  // Get the most severe error level
  const getSeverity = () => {
    if (categorizedErrors.error.length > 0) return 'error'
    if (categorizedErrors.warning.length > 0) return 'warning'
    return 'info'
  }

  // Get appropriate icon for error type
  const getErrorIcon = (errorType) => {
    switch (errorType) {
      case 'VALIDATION_ERROR':
      case 'PARAMETER_VALIDATION_ERROR':
        return <Warning />
      case 'FILE_ERROR':
      case 'SAVE_ERROR':
        return <Error />
      case 'INVALID_FILE_STRUCTURE':
      case 'MISSING_REQUIRED_FIELD':
        return <Error />
      default:
        return <Info />
    }
  }

  // Get user-friendly error message
  const getUserFriendlyMessage = (error) => {
    const messages = {
      'INVALID_FILE_STRUCTURE': 'The file structure is corrupted or invalid',
      'MISSING_REQUIRED_FIELD': `Required field "${error.field}" is missing`,
      'INVALID_JSON': 'The file contains invalid JSON data',
      'PARAMETER_OUT_OF_RANGE': `Parameter "${error.parameter}" value is out of valid range`,
      'INVALID_PARAMETER_TYPE': `Parameter "${error.parameter}" has an invalid type`,
      'INVALID_SIGNAL_CHAIN': 'The signal chain configuration is invalid',
      'MISSING_REQUIRED_BLOCK': `Required block "${error.block || error.blocks?.join(', ')}" is missing`,
      'INVALID_ROUTING': `Invalid signal routing: "${error.routing}"`,
      'STRING_TOO_LONG': `Text is too long (maximum ${error.maxLength} characters)`,
      'INVALID_CHARACTERS': 'Contains invalid or unsupported characters',
      'FILE_NOT_FOUND': 'The specified file could not be found',
      'PERMISSION_DENIED': 'Permission denied - check file permissions',
      'DISK_FULL': 'Not enough disk space to save the file',
      'NETWORK_ERROR': 'Network error - check your connection'
    }

    return messages[error.type] || error.message || 'An unknown error occurred'
  }

  // Get suggested actions for error types
  const getSuggestedActions = (error) => {
    const actions = {
      'PARAMETER_OUT_OF_RANGE': `Adjust the value to be between ${error.range}`,
      'MISSING_REQUIRED_BLOCK': 'Add the required blocks to the signal chain',
      'INVALID_SIGNAL_CHAIN': 'Check the signal chain configuration and block order',
      'STRING_TOO_LONG': 'Shorten the text or use abbreviations',
      'INVALID_CHARACTERS': 'Remove special characters and use only standard text',
      'FILE_NOT_FOUND': 'Check that the file path is correct and the file exists',
      'PERMISSION_DENIED': 'Check file permissions or run as administrator',
      'DISK_FULL': 'Free up disk space and try again'
    }

    return actions[error.type]
  }

  // Render individual error
  const renderError = (error, index) => {
    const userMessage = getUserFriendlyMessage(error)
    const suggestedAction = getSuggestedActions(error)
    const severity = error.severity || 'error'

    return (
      <ListItem key={index} className={classes.errorItem}>
        <ListItemIcon>
          {getErrorIcon(error.type)}
        </ListItemIcon>
        <ListItemText
          primary={
            <Box display="flex" alignItems="center" gap={1}>
              <Typography variant="body2">
                {userMessage}
              </Typography>
              <Chip
                size="small"
                label={severity.toUpperCase()}
                className={classes.severityChip}
                color={severity === 'error' ? 'secondary' : 'default'}
              />
            </Box>
          }
          secondary={
            <Box>
              {suggestedAction && (
                <Typography variant="caption" color="textSecondary">
                  Suggestion: {suggestedAction}
                </Typography>
              )}
              {error.type && (
                <Box className={classes.errorCode}>
                  Error Code: {error.type}
                </Box>
              )}
            </Box>
          }
        />
      </ListItem>
    )
  }

  // Handle bug report
  const handleBugReport = () => {
    setReportDialogOpen(true)
  }

  // Generate bug report data
  const generateBugReport = () => {
    return {
      timestamp: new Date().toISOString(),
      errors: errors,
      userAgent: navigator.userAgent,
      url: window.location.href
    }
  }

  // Download bug report
  const downloadBugReport = () => {
    const reportData = generateBugReport()
    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `headrush-editor-error-report-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    setReportDialogOpen(false)
  }

  const severity = getSeverity()
  const totalErrors = errors.length

  return (
    <Box className={classes.errorContainer}>
      <Alert 
        severity={severity}
        action={
          <Button
            color="inherit"
            size="small"
            onClick={() => setExpanded(!expanded)}
            endIcon={expanded ? <ExpandLess /> : <ExpandMore />}
          >
            {expanded ? 'Hide' : 'Show'} Details
          </Button>
        }
      >
        <AlertTitle>{title}</AlertTitle>
        {totalErrors === 1 ? (
          getUserFriendlyMessage(errors[0])
        ) : (
          `Found ${totalErrors} issues that need attention`
        )}
      </Alert>

      <Collapse in={expanded}>
        <Box className={classes.errorDetails}>
          <List className={classes.errorList}>
            {errors.map((error, index) => renderError(error, index))}
          </List>

          <Box className={classes.errorActions}>
            {onRetry && (
              <Button
                size="small"
                startIcon={<Refresh />}
                onClick={onRetry}
              >
                Retry
              </Button>
            )}
            
            <Button
              size="small"
              startIcon={<BugReport />}
              onClick={handleBugReport}
            >
              Report Issue
            </Button>

            {onDismiss && (
              <Button
                size="small"
                onClick={onDismiss}
              >
                Dismiss
              </Button>
            )}
          </Box>
        </Box>
      </Collapse>

      {/* Bug Report Dialog */}
      <Dialog
        open={reportDialogOpen}
        onClose={() => setReportDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Report Issue</DialogTitle>
        <DialogContent>
          <Typography variant="body2" paragraph>
            This will generate a detailed error report that you can send to the developers
            to help improve the HeadRush Editor.
          </Typography>
          <Typography variant="body2" color="textSecondary">
            The report includes error details, browser information, and current page URL.
            No personal data or file contents are included.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReportDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={downloadBugReport}
            startIcon={<GetApp />}
            color="primary"
          >
            Download Report
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ErrorHandler
