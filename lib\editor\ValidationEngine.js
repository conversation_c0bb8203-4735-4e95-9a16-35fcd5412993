const debug = require('debug')('hb:lib:editor:validation')

/**
 * Comprehensive validation engine for HeadRush data structures
 * Ensures data integrity and compatibility with HeadRush hardware
 */
class ValidationEngine {
  constructor() {
    this.validationRules = {
      // File structure validation rules
      fileStructure: {
        requiredFields: ['content'],
        contentMustBeJSON: true
      },
      
      // Preset validation rules
      preset: {
        requiredDataFields: ['childorder', 'children'],
        maxPresetNameLength: 32,
        requiredParameters: ['PresetName']
      },
      
      // Rig validation rules
      rig: {
        requiredPatchFields: ['childorder', 'children'],
        requiredBlocks: ['Input', 'Output'],
        maxSignalChainLength: 12,
        validRoutingTypes: ['S', 'PS-1', 'PS-2']
      },
      
      // Parameter validation rules
      parameters: {
        numeric: {
          mustBeFinite: true,
          ranges: {
            percentage: { min: 0, max: 100 },
            decibel: { min: -60, max: 20 },
            frequency: { min: 0.1, max: 20000 },
            time: { min: 0, max: 5000 }
          }
        },
        string: {
          maxLength: 255,
          noControlCharacters: true
        },
        boolean: {
          validValues: [true, false]
        }
      }
    }
    
    this.errorMessages = {
      INVALID_FILE_STRUCTURE: 'File structure is invalid or corrupted',
      MISSING_REQUIRED_FIELD: 'Required field is missing',
      INVALID_JSON: 'Content is not valid JSON',
      PARAMETER_OUT_OF_RANGE: 'Parameter value is out of valid range',
      INVALID_PARAMETER_TYPE: 'Parameter type is invalid',
      INVALID_SIGNAL_CHAIN: 'Signal chain configuration is invalid',
      MISSING_REQUIRED_BLOCK: 'Required block is missing from rig',
      INVALID_ROUTING: 'Signal routing configuration is invalid',
      STRING_TOO_LONG: 'String parameter exceeds maximum length',
      INVALID_CHARACTERS: 'String contains invalid characters'
    }
  }

  /**
   * Validate a complete preset file
   * @param {Object} presetData - Preset data to validate
   * @returns {Object} Validation result with success flag and errors
   */
  validatePreset(presetData) {
    debug('Validating preset data')
    const errors = []
    
    try {
      // Validate file structure
      const structureErrors = this.validateFileStructure(presetData)
      errors.push(...structureErrors)
      
      if (structureErrors.length > 0) {
        return { success: false, errors }
      }
      
      // Parse content and validate preset-specific structure
      const content = JSON.parse(presetData.content)
      const presetErrors = this.validatePresetStructure(content)
      errors.push(...presetErrors)
      
      // Validate individual parameters
      const paramErrors = this.validatePresetParameters(content)
      errors.push(...paramErrors)
      
    } catch (error) {
      debug(`Preset validation error: ${error.message}`)
      errors.push({
        type: 'VALIDATION_ERROR',
        message: `Validation failed: ${error.message}`
      })
    }
    
    const success = errors.length === 0
    debug(`Preset validation ${success ? 'passed' : 'failed'} with ${errors.length} errors`)
    
    return { success, errors }
  }

  /**
   * Validate a complete rig file
   * @param {Object} rigData - Rig data to validate
   * @returns {Object} Validation result with success flag and errors
   */
  validateRig(rigData) {
    debug('Validating rig data')
    const errors = []
    
    try {
      // Validate file structure
      const structureErrors = this.validateFileStructure(rigData)
      errors.push(...structureErrors)
      
      if (structureErrors.length > 0) {
        return { success: false, errors }
      }
      
      // Parse content and validate rig-specific structure
      const content = JSON.parse(rigData.content)
      const rigErrors = this.validateRigStructure(content)
      errors.push(...rigErrors)
      
      // Validate signal chain
      const chainErrors = this.validateSignalChain(content)
      errors.push(...chainErrors)
      
      // Validate individual blocks
      const blockErrors = this.validateRigBlocks(content)
      errors.push(...blockErrors)
      
    } catch (error) {
      debug(`Rig validation error: ${error.message}`)
      errors.push({
        type: 'VALIDATION_ERROR',
        message: `Validation failed: ${error.message}`
      })
    }
    
    const success = errors.length === 0
    debug(`Rig validation ${success ? 'passed' : 'failed'} with ${errors.length} errors`)
    
    return { success, errors }
  }

  /**
   * Validate a single parameter value
   * @param {string} paramName - Parameter name
   * @param {*} value - Parameter value
   * @param {string} blockType - Type of block (for context-specific validation)
   * @returns {Object} Validation result
   */
  validateParameterValue(paramName, value, blockType = null) {
    debug(`Validating parameter ${paramName} = ${value}`)
    const errors = []
    
    try {
      // Get parameter type and constraints
      const paramType = this.getParameterType(paramName, value)
      const constraints = this.getParameterConstraints(paramName, blockType)
      
      // Validate based on parameter type
      switch (paramType) {
        case 'numeric':
          const numericErrors = this.validateNumericParameter(paramName, value, constraints)
          errors.push(...numericErrors)
          break
          
        case 'string':
          const stringErrors = this.validateStringParameter(paramName, value, constraints)
          errors.push(...stringErrors)
          break
          
        case 'boolean':
          const booleanErrors = this.validateBooleanParameter(paramName, value)
          errors.push(...booleanErrors)
          break
          
        default:
          debug(`Unknown parameter type for ${paramName}`)
      }
      
    } catch (error) {
      errors.push({
        type: 'PARAMETER_VALIDATION_ERROR',
        parameter: paramName,
        message: error.message
      })
    }
    
    return { success: errors.length === 0, errors }
  }

  /**
   * Validate basic file structure
   * @param {Object} fileData - File data to validate
   * @returns {Array} Array of validation errors
   */
  validateFileStructure(fileData) {
    const errors = []
    
    // Check required fields
    if (!fileData || typeof fileData !== 'object') {
      errors.push({
        type: 'INVALID_FILE_STRUCTURE',
        message: this.errorMessages.INVALID_FILE_STRUCTURE
      })
      return errors
    }
    
    // Check for content field
    if (!fileData.hasOwnProperty('content')) {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'content',
        message: this.errorMessages.MISSING_REQUIRED_FIELD
      })
      return errors
    }
    
    // Validate JSON content
    try {
      JSON.parse(fileData.content)
    } catch (error) {
      errors.push({
        type: 'INVALID_JSON',
        message: this.errorMessages.INVALID_JSON
      })
    }
    
    return errors
  }

  /**
   * Validate preset-specific structure
   * @param {Object} content - Parsed content object
   * @returns {Array} Array of validation errors
   */
  validatePresetStructure(content) {
    const errors = []
    
    if (!content.data || typeof content.data !== 'object') {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'data',
        message: 'Preset data field is missing or invalid'
      })
      return errors
    }
    
    // Get the first (and typically only) data entry
    const dataValues = Object.values(content.data)
    if (dataValues.length === 0) {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'data entries',
        message: 'No preset data entries found'
      })
      return errors
    }
    
    const presetData = dataValues[0]
    
    // Validate required fields
    if (!presetData.childorder || !Array.isArray(presetData.childorder)) {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'childorder',
        message: 'Preset childorder is missing or invalid'
      })
    }
    
    if (!presetData.children || typeof presetData.children !== 'object') {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'children',
        message: 'Preset children is missing or invalid'
      })
    }
    
    return errors
  }

  /**
   * Validate rig-specific structure
   * @param {Object} content - Parsed content object
   * @returns {Array} Array of validation errors
   */
  validateRigStructure(content) {
    const errors = []
    
    if (!content.data || !content.data.Patch) {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'data.Patch',
        message: 'Rig Patch data is missing'
      })
      return errors
    }
    
    const { Patch } = content.data
    
    // Validate Patch structure
    if (!Patch.childorder || !Array.isArray(Patch.childorder)) {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'Patch.childorder',
        message: 'Rig childorder is missing or invalid'
      })
    }
    
    if (!Patch.children || typeof Patch.children !== 'object') {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        field: 'Patch.children',
        message: 'Rig children is missing or invalid'
      })
    }
    
    return errors
  }

  /**
   * Validate signal chain configuration
   * @param {Object} content - Parsed content object
   * @returns {Array} Array of validation errors
   */
  validateSignalChain(content) {
    const errors = []
    
    try {
      const { Patch } = content.data
      const { childorder, children } = Patch
      
      // Check signal chain length
      if (childorder.length > this.validationRules.rig.maxSignalChainLength) {
        errors.push({
          type: 'INVALID_SIGNAL_CHAIN',
          message: `Signal chain too long (${childorder.length} > ${this.validationRules.rig.maxSignalChainLength})`
        })
      }
      
      // Check for required blocks
      const requiredBlocks = this.validationRules.rig.requiredBlocks
      const missingBlocks = requiredBlocks.filter(block => !childorder.includes(block))
      if (missingBlocks.length > 0) {
        errors.push({
          type: 'MISSING_REQUIRED_BLOCK',
          blocks: missingBlocks,
          message: `Missing required blocks: ${missingBlocks.join(', ')}`
        })
      }
      
      // Validate routing configuration
      if (children.Chain && children.Chain.children && children.Chain.children.Routing) {
        const routing = children.Chain.children.Routing.string
        if (!this.validationRules.rig.validRoutingTypes.includes(routing)) {
          errors.push({
            type: 'INVALID_ROUTING',
            routing: routing,
            message: `Invalid routing type: ${routing}`
          })
        }
      }
      
    } catch (error) {
      errors.push({
        type: 'INVALID_SIGNAL_CHAIN',
        message: `Signal chain validation failed: ${error.message}`
      })
    }
    
    return errors
  }

  /**
   * Validate all blocks in a rig
   * @param {Object} content - Parsed content object
   * @returns {Array} Array of validation errors
   */
  validateRigBlocks(content) {
    const errors = []
    
    try {
      const { Patch } = content.data
      const { childorder, children } = Patch
      
      // Validate each block
      for (const blockName of childorder) {
        if (blockName === 'Chain' || blockName === 'Rig') continue
        
        const blockData = children[blockName]
        if (!blockData) {
          errors.push({
            type: 'MISSING_REQUIRED_BLOCK',
            block: blockName,
            message: `Block ${blockName} is referenced but not defined`
          })
          continue
        }
        
        // Validate block structure
        const blockErrors = this.validateBlockStructure(blockName, blockData)
        errors.push(...blockErrors)
      }
      
    } catch (error) {
      errors.push({
        type: 'VALIDATION_ERROR',
        message: `Block validation failed: ${error.message}`
      })
    }
    
    return errors
  }

  /**
   * Validate individual block structure
   * @param {string} blockName - Name of the block
   * @param {Object} blockData - Block data to validate
   * @returns {Array} Array of validation errors
   */
  validateBlockStructure(blockName, blockData) {
    const errors = []
    
    if (!blockData.childorder || !Array.isArray(blockData.childorder)) {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        block: blockName,
        field: 'childorder',
        message: `Block ${blockName} missing childorder`
      })
    }
    
    if (!blockData.children || typeof blockData.children !== 'object') {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        block: blockName,
        field: 'children',
        message: `Block ${blockName} missing children`
      })
    }
    
    return errors
  }

  /**
   * Get parameter type based on name and value
   * @param {string} paramName - Parameter name
   * @param {*} value - Parameter value
   * @returns {string} Parameter type
   */
  getParameterType(paramName, value) {
    if (typeof value === 'boolean') return 'boolean'
    if (typeof value === 'string') return 'string'
    if (typeof value === 'number') return 'numeric'
    return 'unknown'
  }

  /**
   * Get parameter constraints based on name and context
   * @param {string} paramName - Parameter name
   * @param {string} blockType - Block type for context
   * @returns {Object} Parameter constraints
   */
  getParameterConstraints(paramName, blockType) {
    // This would be expanded with comprehensive parameter constraints
    const defaultConstraints = {
      numeric: { min: -1000, max: 1000 },
      string: { maxLength: 255 },
      boolean: { validValues: [true, false] }
    }
    
    // Add parameter-specific constraints here
    // This is a simplified version - would be expanded based on HeadRush specifications
    
    return defaultConstraints
  }

  /**
   * Validate numeric parameter
   * @param {string} paramName - Parameter name
   * @param {number} value - Parameter value
   * @param {Object} constraints - Parameter constraints
   * @returns {Array} Array of validation errors
   */
  validateNumericParameter(paramName, value, constraints) {
    const errors = []
    
    if (!Number.isFinite(value)) {
      errors.push({
        type: 'INVALID_PARAMETER_TYPE',
        parameter: paramName,
        message: `Parameter ${paramName} must be a finite number`
      })
      return errors
    }
    
    if (constraints.numeric && (value < constraints.numeric.min || value > constraints.numeric.max)) {
      errors.push({
        type: 'PARAMETER_OUT_OF_RANGE',
        parameter: paramName,
        value: value,
        range: `${constraints.numeric.min} - ${constraints.numeric.max}`,
        message: `Parameter ${paramName} value ${value} is out of range`
      })
    }
    
    return errors
  }

  /**
   * Validate string parameter
   * @param {string} paramName - Parameter name
   * @param {string} value - Parameter value
   * @param {Object} constraints - Parameter constraints
   * @returns {Array} Array of validation errors
   */
  validateStringParameter(paramName, value, constraints) {
    const errors = []
    
    if (typeof value !== 'string') {
      errors.push({
        type: 'INVALID_PARAMETER_TYPE',
        parameter: paramName,
        message: `Parameter ${paramName} must be a string`
      })
      return errors
    }
    
    if (constraints.string && value.length > constraints.string.maxLength) {
      errors.push({
        type: 'STRING_TOO_LONG',
        parameter: paramName,
        length: value.length,
        maxLength: constraints.string.maxLength,
        message: `Parameter ${paramName} exceeds maximum length`
      })
    }
    
    // Check for control characters
    if (/[\x00-\x1F\x7F]/.test(value)) {
      errors.push({
        type: 'INVALID_CHARACTERS',
        parameter: paramName,
        message: `Parameter ${paramName} contains invalid characters`
      })
    }
    
    return errors
  }

  /**
   * Validate boolean parameter
   * @param {string} paramName - Parameter name
   * @param {boolean} value - Parameter value
   * @returns {Array} Array of validation errors
   */
  validateBooleanParameter(paramName, value) {
    const errors = []
    
    if (typeof value !== 'boolean') {
      errors.push({
        type: 'INVALID_PARAMETER_TYPE',
        parameter: paramName,
        message: `Parameter ${paramName} must be a boolean`
      })
    }
    
    return errors
  }

  /**
   * Validate preset parameters
   * @param {Object} content - Parsed content object
   * @returns {Array} Array of validation errors
   */
  validatePresetParameters(content) {
    const errors = []
    
    try {
      const dataValues = Object.values(content.data)
      const { children } = dataValues[0]
      
      // Validate each parameter
      for (const [paramName, paramData] of Object.entries(children)) {
        const value = paramData.value || paramData.string || paramData.state
        const validation = this.validateParameterValue(paramName, value)
        if (!validation.success) {
          errors.push(...validation.errors)
        }
      }
      
    } catch (error) {
      errors.push({
        type: 'VALIDATION_ERROR',
        message: `Parameter validation failed: ${error.message}`
      })
    }
    
    return errors
  }
}

module.exports = ValidationEngine
