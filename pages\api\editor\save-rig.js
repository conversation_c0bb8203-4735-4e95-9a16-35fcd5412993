const { editor } = require('../../../lib/editor')
const debug = require('debug')('hb:api:save-rig')

/**
 * API endpoint for saving rig files
 * POST /api/editor/save-rig
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    })
  }

  try {
    const { filePath, rigData, options = {} } = req.body

    // Validate required fields
    if (!filePath || !rigData) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: filePath and rigData'
      })
    }

    debug(`Saving rig to ${filePath}`)

    // Validate the rig data before saving
    const validation = editor.validator.validateRig(rigData)
    if (!validation.success) {
      debug(`Rig validation failed: ${validation.errors.length} errors`)
      return res.status(400).json({
        success: false,
        error: 'Rig validation failed',
        validationErrors: validation.errors
      })
    }

    // Save the rig
    const result = await editor.fileManager.saveRig(filePath, rigData, options)

    if (result.success) {
      debug(`Rig saved successfully to ${filePath}`)
      return res.status(200).json(result)
    } else {
      debug(`Failed to save rig: ${result.error}`)
      return res.status(500).json(result)
    }

  } catch (error) {
    debug(`Error in save-rig API: ${error.message}`)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    })
  }
}

// Configure API route
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb', // Allow larger rig files
    },
  },
}
