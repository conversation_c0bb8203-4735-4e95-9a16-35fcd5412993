const { editor } = require('../../../lib/editor')
const debug = require('debug')('hb:api:validate')

/**
 * API endpoint for validating preset and rig data
 * POST /api/editor/validate
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    })
  }

  try {
    const { data, type } = req.body

    // Validate required fields
    if (!data || !type) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: data and type'
      })
    }

    // Validate type
    if (!['preset', 'rig'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid type. Must be "preset" or "rig"'
      })
    }

    debug(`Validating ${type} data`)

    // Perform validation
    let validation
    if (type === 'preset') {
      validation = editor.validator.validatePreset(data)
    } else {
      validation = editor.validator.validateRig(data)
    }

    debug(`Validation result: ${validation.success ? 'passed' : 'failed'} with ${validation.errors?.length || 0} errors`)

    return res.status(200).json({
      success: true,
      validation: validation
    })

  } catch (error) {
    debug(`Error in validate API: ${error.message}`)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    })
  }
}
