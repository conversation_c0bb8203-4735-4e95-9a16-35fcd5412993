{"name": "headrush-browser", "version": "0.1.0", "description": "Read-only viewer of HeadRush Pedalboard Rigs and Blocks", "homepage": "https://github.com/rockrep/headrush-browser#readme", "bugs": {"url": "https://github.com/rockrep/headrush-browser/issues", "email": "micha<PERSON><EMAIL>"}, "author": {"name": "<PERSON>", "email": "micha<PERSON><EMAIL>", "url": "https://github.com/rockrep"}, "contributors": [], "private": true, "license": "0BSD", "scripts": {"dev": "next dev", "debug": "DEBUG=${DEBUG:-hb:*} next dev", "build": "next build", "start": "next start", "prettier": "prettier -l pages/**/*.js helpers/**/*.js lib/**/*.js components/**/*.js"}, "dependencies": {"@material-ui/core": "^4.11.3", "classnames": "^2.3.1", "cookie": "^0.4.1", "debug": "^4.3.1", "formik": "^2.2.6", "formik-material-ui": "^3.0.1", "next": "10.1.3", "react": "17.0.2", "react-cookie": "^4.0.3", "react-dom": "17.0.2"}, "devDependencies": {"eslint": "^7.23.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-flowtype": "^5.6.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.3.4", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.23.1", "eslint-plugin-react-hooks": "^4.2.0", "prettier": "^2.2.1"}}