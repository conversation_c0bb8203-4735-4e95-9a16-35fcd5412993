import React, { useState, useEffect } from 'react'
import { 
  TextField, 
  Slider, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Switch, 
  FormControlLabel,
  Typography,
  Box,
  Tooltip
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import { suffix } from '../../lib'

const useStyles = makeStyles((theme) => ({
  parameterControl: {
    margin: theme.spacing(1),
    minWidth: 200,
  },
  sliderContainer: {
    padding: theme.spacing(1),
  },
  parameterLabel: {
    fontWeight: 'bold',
    marginBottom: theme.spacing(0.5),
  },
  valueDisplay: {
    fontSize: '0.875rem',
    color: theme.palette.text.secondary,
  },
  errorText: {
    color: theme.palette.error.main,
    fontSize: '0.75rem',
    marginTop: theme.spacing(0.5),
  },
  validationIcon: {
    marginLeft: theme.spacing(0.5),
  }
}))

/**
 * Generic parameter control component that renders appropriate input based on parameter type
 */
const ParameterControl = ({
  paramName,
  value,
  blockType,
  onChange,
  onValidation,
  disabled = false,
  showValidation = true
}) => {
  const classes = useStyles()
  const [localValue, setLocalValue] = useState(value)
  const [validationError, setValidationError] = useState(null)
  const [isValid, setIsValid] = useState(true)

  // Update local value when prop changes
  useEffect(() => {
    setLocalValue(value)
  }, [value])

  // Get parameter configuration
  const paramConfig = getParameterConfig(paramName, blockType)
  const paramSuffix = suffix(paramName, blockType)

  // Handle value change
  const handleValueChange = (newValue) => {
    setLocalValue(newValue)
    
    // Validate the new value
    const validation = validateParameterValue(paramName, newValue, paramConfig)
    setValidationError(validation.error)
    setIsValid(validation.isValid)
    
    // Call validation callback
    if (onValidation) {
      onValidation(paramName, validation)
    }
    
    // Call change callback if valid
    if (validation.isValid && onChange) {
      onChange(paramName, newValue)
    }
  }

  // Handle blur event for text inputs (commit changes)
  const handleBlur = () => {
    if (isValid && onChange && localValue !== value) {
      onChange(paramName, localValue)
    }
  }

  // Render appropriate control based on parameter type
  const renderControl = () => {
    switch (paramConfig.controlType) {
      case 'slider':
        return renderSlider()
      case 'select':
        return renderSelect()
      case 'boolean':
        return renderBoolean()
      case 'text':
        return renderText()
      default:
        return renderText()
    }
  }

  const renderSlider = () => (
    <Box className={classes.sliderContainer}>
      <Typography className={classes.parameterLabel}>
        {paramName}
      </Typography>
      <Slider
        value={localValue}
        onChange={(event, newValue) => handleValueChange(newValue)}
        min={paramConfig.min}
        max={paramConfig.max}
        step={paramConfig.step || 1}
        disabled={disabled}
        valueLabelDisplay="auto"
        valueLabelFormat={(val) => `${val}${paramSuffix}`}
      />
      <Typography className={classes.valueDisplay}>
        {localValue}{paramSuffix}
        {paramConfig.range && ` (${paramConfig.min} - ${paramConfig.max})`}
      </Typography>
    </Box>
  )

  const renderSelect = () => (
    <FormControl className={classes.parameterControl} disabled={disabled}>
      <InputLabel>{paramName}</InputLabel>
      <Select
        value={localValue}
        onChange={(event) => handleValueChange(event.target.value)}
      >
        {paramConfig.options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )

  const renderBoolean = () => (
    <FormControlLabel
      control={
        <Switch
          checked={localValue}
          onChange={(event) => handleValueChange(event.target.checked)}
          disabled={disabled}
        />
      }
      label={paramName}
      className={classes.parameterControl}
    />
  )

  const renderText = () => (
    <TextField
      label={paramName}
      value={localValue}
      onChange={(event) => handleValueChange(event.target.value)}
      onBlur={handleBlur}
      disabled={disabled}
      className={classes.parameterControl}
      error={!isValid}
      helperText={validationError || (paramSuffix && `Units: ${paramSuffix.trim()}`)}
      inputProps={{
        maxLength: paramConfig.maxLength || 255
      }}
    />
  )

  return (
    <Box>
      {renderControl()}
      {showValidation && validationError && (
        <Typography className={classes.errorText}>
          {validationError}
        </Typography>
      )}
    </Box>
  )
}

/**
 * Get parameter configuration based on name and block type
 */
const getParameterConfig = (paramName, blockType) => {
  const configs = {
    // Percentage-based parameters (0-100)
    'Drive': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Tone': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Level': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Bass': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Mid': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Treb': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Master': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Mix': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Depth': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Feedback': { controlType: 'slider', min: 0, max: 100, step: 1 },
    'Volume': { controlType: 'slider', min: 0, max: 100, step: 1 },
    
    // Decibel-based parameters
    'Gain': { controlType: 'slider', min: -20, max: 20, step: 0.1 },
    'RigVolume': { controlType: 'slider', min: -20, max: 20, step: 0.1 },
    'Thresh': { controlType: 'slider', min: -60, max: 0, step: 1 },
    'Knee': { controlType: 'slider', min: 0, max: 10, step: 0.1 },
    
    // Frequency-based parameters
    'Freq': { controlType: 'slider', min: 20, max: 20000, step: 10 },
    'HiCut': { controlType: 'slider', min: 1000, max: 20000, step: 100 },
    'LoCut': { controlType: 'slider', min: 20, max: 1000, step: 10 },
    'Rate': { controlType: 'slider', min: 0.1, max: 10, step: 0.1 },
    
    // Time-based parameters
    'Release': { controlType: 'slider', min: 1, max: 1000, step: 1 },
    'PreDelay': { controlType: 'slider', min: 0, max: 500, step: 1 },
    'Delay': { controlType: 'slider', min: 1, max: 2000, step: 1 },
    
    // String parameters
    'PresetName': { controlType: 'text', maxLength: 32 },
    
    // Boolean parameters
    'Doubling': { controlType: 'boolean' },
    
    // Color selection
    'Colour': {
      controlType: 'select',
      options: [
        { value: 'Yellow', label: 'Yellow' },
        { value: 'Orange', label: 'Orange' },
        { value: 'Blue', label: 'Blue' },
        { value: 'Green', label: 'Green' },
        { value: 'Red', label: 'Red' },
        { value: 'Purple', label: 'Purple' },
        { value: 'Pink', label: 'Pink' },
        { value: 'Dark Green', label: 'Dark Green' },
        { value: 'Light Blue', label: 'Light Blue' }
      ]
    }
  }
  
  // Return specific config or default text config
  return configs[paramName] || { controlType: 'text', maxLength: 255 }
}

/**
 * Validate parameter value
 */
const validateParameterValue = (paramName, value, config) => {
  try {
    switch (config.controlType) {
      case 'slider':
        if (typeof value !== 'number' || !Number.isFinite(value)) {
          return { isValid: false, error: 'Must be a valid number' }
        }
        if (value < config.min || value > config.max) {
          return { 
            isValid: false, 
            error: `Value must be between ${config.min} and ${config.max}` 
          }
        }
        break
        
      case 'text':
        if (typeof value !== 'string') {
          return { isValid: false, error: 'Must be text' }
        }
        if (config.maxLength && value.length > config.maxLength) {
          return { 
            isValid: false, 
            error: `Maximum length is ${config.maxLength} characters` 
          }
        }
        // Check for invalid characters
        if (/[\x00-\x1F\x7F]/.test(value)) {
          return { isValid: false, error: 'Contains invalid characters' }
        }
        break
        
      case 'boolean':
        if (typeof value !== 'boolean') {
          return { isValid: false, error: 'Must be true or false' }
        }
        break
        
      case 'select':
        const validValues = config.options.map(opt => opt.value)
        if (!validValues.includes(value)) {
          return { isValid: false, error: 'Invalid selection' }
        }
        break
    }
    
    return { isValid: true, error: null }
    
  } catch (error) {
    return { isValid: false, error: 'Validation error' }
  }
}

export default ParameterControl
