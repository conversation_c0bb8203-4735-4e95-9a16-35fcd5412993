# HeadRush Editor - Architecture Analysis

## Current Architecture Overview

### Technology Stack
- **Framework**: Next.js (React-based)
- **UI Library**: Material-UI v4
- **State Management**: React hooks + cookies for persistence
- **File Operations**: Node.js fs module (server-side)
- **Styling**: CSS Modules

### Current File Structure
```
/pages
  ├── index.js          # Home page with path selection
  ├── browser.js        # Main browser interface
  └── _app.js           # App wrapper

/components
  ├── Blocks.js         # Block list navigation
  ├── Rigs.js           # Rig list navigation
  ├── Preset.js         # Individual preset display
  ├── Rig.js            # Rig display with signal chain
  ├── RigBlock.js       # Individual block within rig
  ├── Viewer.js         # Main content viewer
  └── NavList.js        # Generic navigation list

/lib
  └── index.js          # Core parsing utilities

/helpers
  └── index.js          # Cookie parsing utilities
```

## HeadRush File Format Analysis

### Directory Structure
```
HeadRush Root/
├── Blocks/
│   ├── Amp/
│   │   ├── preset1/    # JSON file (no extension)
│   │   └── preset2/
│   ├── Delay/
│   └── [other effects]/
└── Rigs/
    ├── rig1.rig
    └── rig2.rig
```

### File Format Structure

#### Preset Files (Blocks)
```json
{
  "content": "{\"data\":{\"[UUID]\":{\"childorder\":[\"PresetName\",\"Drive\",\"Tone\"],\"children\":{\"PresetName\":{\"type\":0,\"string\":\"Clean\"},\"Drive\":{\"type\":0,\"value\":25.5},\"Tone\":{\"type\":0,\"value\":50.0}}}}}"
}
```

#### Rig Files
```json
{
  "content": "{\"data\":{\"Patch\":{\"childorder\":[\"Input\",\"Amp\",\"Cab\",\"Output\"],\"children\":{\"Chain\":{\"children\":{\"Routing\":{\"string\":\"S\"}}},\"Amp\":{\"childorder\":[\"PresetName\",\"Drive\"],\"children\":{\"PresetName\":{\"string\":\"Rock\"},\"Drive\":{\"value\":75.0}}}}}}}"
}
```

### Data Type System
Based on `lib/index.js` analysis:
- **Type 1**: Boolean (ON/OFF states)
- **Type 0**: Numeric or string values
- **Properties**: `type`, `state`, `string`, `value`

### Current Parsing Logic
- `childParser()`: Converts raw data to display format
- `suffix()`: Adds appropriate units (dB, Hz, ms, %, etc.)
- `blockColor()`: Maps color settings to CSS colors

## Key Insights for Editor Implementation

### 1. File Operations
- Currently read-only server-side operations
- Need to add write operations with validation
- Backup mechanism required for safety

### 2. Data Structure Integrity
- Nested JSON with specific schema
- UUID-based keys in some contexts
- Order preservation important (`childorder` arrays)

### 3. Signal Chain Management
- Block order defined in `childorder`
- Routing types: 'S' (Serial), 'PS-1' (Parallel-Serial)
- Special handling for doubled blocks (Amp/Cab)

### 4. Validation Requirements
- Type consistency (numeric ranges, string formats)
- Block compatibility checks
- Signal routing validation

## Recommendations for Editor Architecture

### 1. Preserve Existing Structure
- Keep current read-only functionality intact
- Add editor as optional mode/feature
- Maintain backward compatibility

### 2. Add New Components
- `PresetEditor.js` - Parameter editing interface
- `RigEditor.js` - Signal chain editing
- `FileManager.js` - Save/backup operations
- `ValidationEngine.js` - Data integrity checks

### 3. State Management Enhancement
- Add editing state management
- Implement undo/redo functionality
- Track dirty state for unsaved changes

### 4. API Extensions
- Add POST/PUT endpoints for file operations
- Implement validation middleware
- Add backup/restore functionality
